"""
Modern UI for 1ClickVideo Application
A sleek, contemporary interface with Material Design principles
"""

import os
import re
import json
import time
import datetime
import webbrowser
import sys
import platform
import subprocess
import threading
from pathlib import Path

import tkinter as tk
from tkinter import ttk, messagebox, font, colorchooser, filedialog
from ttkthemes import ThemedTk

# Import all existing functionality from main.py
from main import (
    VideoGeneratorThread, switch_ai_provider, process_custom_script, process_custom_title,
    AI_PROVIDERS, TTS_PROVIDERS, get_available_fonts, config, script_dir,
    APP_NAME, APP_VERSION, DEFAULT_FONT
)
import ai_client
import voice_preview
from voice_selection_widget import VoiceSelectionWidget
from auth import show_login_dialog, get_current_user_email, logout_user
from api import replicate_flux_api, fal_flux_api, together_flux_api

class ModernColors:
    """Modern color palette inspired by contemporary design systems"""

    # Primary brand colors
    PRIMARY = "#6366F1"          # Indigo - main brand color
    PRIMARY_HOVER = "#4F46E5"    # Darker indigo for hover states
    PRIMARY_LIGHT = "#A5B4FC"    # Light indigo for accents

    # Secondary colors
    SECONDARY = "#10B981"        # Emerald green
    SECONDARY_HOVER = "#059669"  # Darker emerald

    # Accent colors
    ACCENT = "#F59E0B"          # Amber
    ACCENT_HOVER = "#D97706"    # Darker amber

    # Status colors
    SUCCESS = "#10B981"         # Green
    WARNING = "#F59E0B"         # Amber
    ERROR = "#EF4444"           # Red
    INFO = "#3B82F6"            # Blue

    # Background colors (Dark theme)
    BG_PRIMARY = "#0F172A"      # Very dark blue-gray
    BG_SECONDARY = "#1E293B"    # Dark blue-gray
    BG_TERTIARY = "#334155"     # Medium blue-gray

    # Surface colors
    SURFACE = "#1E293B"         # Card backgrounds
    SURFACE_HOVER = "#334155"   # Hover state for cards

    # Text colors
    TEXT_PRIMARY = "#F8FAFC"    # Almost white
    TEXT_SECONDARY = "#CBD5E1"  # Light gray
    TEXT_MUTED = "#64748B"      # Medium gray

    # Border colors
    BORDER = "#334155"          # Subtle borders
    BORDER_LIGHT = "#475569"    # Lighter borders for focus states

class ModernButton(tk.Button):
    """Modern button with hover effects and contemporary styling"""

    def __init__(self, parent, text="Button", command=None, style="primary", width=None, **kwargs):
        self.style_type = style
        self.parent = parent

        # Define style configurations
        styles = {
            "primary": {
                "bg": ModernColors.PRIMARY,
                "fg": ModernColors.TEXT_PRIMARY,
                "active_bg": ModernColors.PRIMARY_HOVER,
                "border": 0
            },
            "secondary": {
                "bg": ModernColors.SECONDARY,
                "fg": ModernColors.TEXT_PRIMARY,
                "active_bg": ModernColors.SECONDARY_HOVER,
                "border": 0
            },
            "accent": {
                "bg": ModernColors.ACCENT,
                "fg": ModernColors.TEXT_PRIMARY,
                "active_bg": ModernColors.ACCENT_HOVER,
                "border": 0
            },
            "outline": {
                "bg": ModernColors.BG_SECONDARY,
                "fg": ModernColors.PRIMARY,
                "active_bg": ModernColors.BG_TERTIARY,
                "border": 1
            },
            "ghost": {
                "bg": ModernColors.BG_SECONDARY,
                "fg": ModernColors.TEXT_SECONDARY,
                "active_bg": ModernColors.BG_TERTIARY,
                "border": 0
            }
        }

        style_config = styles.get(style, styles["primary"])

        super().__init__(
            parent,
            text=text,
            command=command,
            bg=style_config["bg"],
            fg=style_config["fg"],
            activebackground=style_config["active_bg"],
            activeforeground=style_config["fg"],
            relief=tk.FLAT,
            borderwidth=style_config["border"],
            font=(DEFAULT_FONT, 11, "bold"),
            padx=20,
            pady=10,
            cursor="hand2",
            width=width or 15,
            **kwargs
        )

        # Add hover effects
        self.bind("<Enter>", self._on_enter)
        self.bind("<Leave>", self._on_leave)

        # Store original colors
        self.original_bg = style_config["bg"]
        self.hover_bg = style_config["active_bg"]

    def _on_enter(self, event):
        """Handle mouse enter"""
        self.config(bg=self.hover_bg)

    def _on_leave(self, event):
        """Handle mouse leave"""
        self.config(bg=self.original_bg)

class ModernColorPicker(tk.Frame):
    """Modern color picker component"""

    def __init__(self, parent, initial_color="#FFFFFF", **kwargs):
        super().__init__(parent, bg=ModernColors.SURFACE, **kwargs)

        self.color = initial_color

        # Color display button
        self.color_button = tk.Button(
            self,
            text="  ",
            bg=self.color,
            width=3,
            height=1,
            relief=tk.FLAT,
            command=self.pick_color,
            cursor="hand2"
        )
        self.color_button.pack(side=tk.LEFT, padx=(0, 10))

        # Color value label
        self.color_label = tk.Label(
            self,
            text=self.color,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10)
        )
        self.color_label.pack(side=tk.LEFT)

    def pick_color(self):
        """Open color picker dialog"""
        color = colorchooser.askcolor(color=self.color, title="Choose Color")
        if color[1]:  # If user didn't cancel
            self.color = color[1]
            self.color_button.configure(bg=self.color)
            self.color_label.configure(text=self.color)

    def get_color(self):
        """Get the current color"""
        return self.color

    def set_color(self, color):
        """Set the color"""
        self.color = color
        self.color_button.configure(bg=self.color)
        self.color_label.configure(text=self.color)

class ModernCard(tk.Frame):
    """Modern card component with subtle shadows and rounded appearance"""

    def __init__(self, parent, title=None, padding=20, **kwargs):
        super().__init__(
            parent,
            bg=ModernColors.SURFACE,
            relief=tk.FLAT,
            bd=1,
            highlightbackground=ModernColors.BORDER,
            highlightthickness=1,
            **kwargs
        )

        self.padding = padding

        # Add internal padding
        self.configure(padx=padding, pady=padding)

        # Add title if provided
        if title:
            title_label = tk.Label(
                self,
                text=title,
                bg=ModernColors.SURFACE,
                fg=ModernColors.TEXT_PRIMARY,
                font=(DEFAULT_FONT, 14, "bold")
            )
            title_label.pack(anchor="w", pady=(0, 15))

class ModernSettingsDialog:
    """Comprehensive settings dialog with modern UI design"""

    def __init__(self, parent, app):
        self.parent = parent
        self.app = app
        self.window = None

        # Settings variables
        self.api_keys = {}
        self.preferences = {}

    def show(self):
        """Show the settings dialog"""
        if self.window and self.window.winfo_exists():
            self.window.lift()
            return

        self.create_window()
        self.load_current_settings()
        self.create_ui()

    def create_window(self):
        """Create the settings window"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("Settings - 1ClickVideo")
        self.window.geometry("800x600")
        self.window.configure(bg=ModernColors.BG_PRIMARY)
        self.window.transient(self.parent)
        self.window.grab_set()

        # Center the window
        self.window.geometry("+%d+%d" % (
            self.parent.winfo_rootx() + 100,
            self.parent.winfo_rooty() + 50
        ))

        # Make window resizable
        self.window.minsize(700, 500)

    def load_current_settings(self):
        """Load current settings from environment and config"""
        # Load API keys from environment
        self.api_keys = {
            'openai': os.getenv('OPENAI_API_KEY', ''),
            'groq': os.getenv('GROQ_API_KEY', ''),
            'elevenlabs': os.getenv('ELEVENLABS_API_KEY', ''),
            'replicate': os.getenv('REPLICATE_API_KEY', ''),
            'fal': os.getenv('FAL_API_KEY', ''),
            'together': os.getenv('TOGETHER_API_KEY', '')
        }

        # Load preferences from config and app state
        self.preferences = {
            'end_pause_duration': self.app.end_pause_var.get(),
            'default_ai_provider': self.app.ai_provider_var.get(),
            'default_tts_provider': self.app.tts_model_var.get(),
            'default_image_model': self.app.image_model_var.get(),
            'default_video_quality': self.app.video_quality_var.get(),
            'default_orientation': self.app.orientation_var.get()
        }

    def create_ui(self):
        """Create the settings UI with tabs"""
        # Main container
        main_frame = tk.Frame(self.window, bg=ModernColors.BG_PRIMARY)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Header
        header_frame = tk.Frame(main_frame, bg=ModernColors.BG_PRIMARY)
        header_frame.pack(fill=tk.X, pady=(0, 20))

        title_label = tk.Label(
            header_frame,
            text="⚙️ Application Settings",
            font=(DEFAULT_FONT, 18, "bold"),
            fg=ModernColors.TEXT_PRIMARY,
            bg=ModernColors.BG_PRIMARY
        )
        title_label.pack(side=tk.LEFT)

        # Create notebook for tabs
        self.notebook = ttk.Notebook(main_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 20))

        # Create tabs
        self.create_api_tab()
        self.create_preferences_tab()
        self.create_advanced_tab()

        # Button frame
        button_frame = tk.Frame(main_frame, bg=ModernColors.BG_PRIMARY)
        button_frame.pack(fill=tk.X)

        # Buttons
        save_btn = ModernButton(
            button_frame,
            text="💾 Save Settings",
            command=self.save_settings,
            style="primary",
            width=15
        )
        save_btn.pack(side=tk.LEFT, padx=(0, 10))

        test_btn = ModernButton(
            button_frame,
            text="🧪 Test APIs",
            command=self.test_apis,
            style="secondary",
            width=15
        )
        test_btn.pack(side=tk.LEFT, padx=(0, 10))

        cancel_btn = ModernButton(
            button_frame,
            text="❌ Cancel",
            command=self.window.destroy,
            style="ghost",
            width=15
        )
        cancel_btn.pack(side=tk.RIGHT)

        reset_btn = ModernButton(
            button_frame,
            text="🔄 Reset",
            command=self.reset_settings,
            style="outline",
            width=15
        )
        reset_btn.pack(side=tk.RIGHT, padx=(0, 10))

    def create_api_tab(self):
        """Create API configuration tab"""
        api_frame = tk.Frame(self.notebook, bg=ModernColors.BG_PRIMARY)
        self.notebook.add(api_frame, text="🔑 API Keys")

        # Scrollable content
        canvas = tk.Canvas(api_frame, bg=ModernColors.BG_PRIMARY, highlightthickness=0)
        scrollbar = ttk.Scrollbar(api_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernColors.BG_PRIMARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # API key entries
        self.api_entries = {}

        # OpenAI API
        openai_card = ModernCard(scrollable_frame, title="OpenAI API")
        openai_card.pack(fill=tk.X, padx=20, pady=(20, 10))

        self.create_api_entry(openai_card, "openai", "OpenAI API Key",
                             "Used for script generation and TTS")

        # Groq API
        groq_card = ModernCard(scrollable_frame, title="Groq API")
        groq_card.pack(fill=tk.X, padx=20, pady=10)

        self.create_api_entry(groq_card, "groq", "Groq API Key",
                             "Alternative AI provider for script generation")

        # ElevenLabs API
        elevenlabs_card = ModernCard(scrollable_frame, title="ElevenLabs API")
        elevenlabs_card.pack(fill=tk.X, padx=20, pady=10)

        self.create_api_entry(elevenlabs_card, "elevenlabs", "ElevenLabs API Key",
                             "High-quality text-to-speech service")

        # Replicate API
        replicate_card = ModernCard(scrollable_frame, title="Replicate API")
        replicate_card.pack(fill=tk.X, padx=20, pady=10)

        self.create_api_entry(replicate_card, "replicate", "Replicate API Key",
                             "Image generation using Flux models")

        # FAL API
        fal_card = ModernCard(scrollable_frame, title="FAL AI API")
        fal_card.pack(fill=tk.X, padx=20, pady=10)

        self.create_api_entry(fal_card, "fal", "FAL API Key",
                             "Fast image generation service")

        # Together API
        together_card = ModernCard(scrollable_frame, title="Together AI API")
        together_card.pack(fill=tk.X, padx=20, pady=(10, 20))

        self.create_api_entry(together_card, "together", "Together API Key",
                             "Image generation and AI models")

    def create_api_entry(self, parent, key, label, description):
        """Create an API key entry widget"""
        # Description
        desc_label = tk.Label(
            parent,
            text=description,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 10),
            wraplength=400
        )
        desc_label.pack(anchor="w", pady=(0, 10))

        # Entry frame
        entry_frame = tk.Frame(parent, bg=ModernColors.SURFACE)
        entry_frame.pack(fill=tk.X, pady=(0, 10))

        # Label
        tk.Label(
            entry_frame,
            text=f"{label}:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).pack(anchor="w", pady=(0, 5))

        # Entry with show/hide functionality
        entry_container = tk.Frame(entry_frame, bg=ModernColors.SURFACE)
        entry_container.pack(fill=tk.X)

        # Create StringVar for the entry
        var = tk.StringVar(value=self.api_keys.get(key, ''))
        self.api_entries[key] = var

        # Entry widget
        entry = tk.Entry(
            entry_container,
            textvariable=var,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            bd=5,
            show="*" if var.get() else ""  # Show asterisks if there's a value
        )
        entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        # Show/Hide button
        show_btn = ModernButton(
            entry_container,
            text="👁️",
            command=lambda: self.toggle_api_visibility(key, entry),
            style="ghost",
            width=3
        )
        show_btn.pack(side=tk.RIGHT)

        # Status indicator
        status_label = tk.Label(
            entry_frame,
            text="✅ Valid" if var.get() else "❌ Not Set",
            bg=ModernColors.SURFACE,
            fg=ModernColors.SUCCESS if var.get() else ModernColors.ERROR,
            font=(DEFAULT_FONT, 9)
        )
        status_label.pack(anchor="w", pady=(5, 0))

        # Store references for updates
        if not hasattr(self, 'api_status_labels'):
            self.api_status_labels = {}
        self.api_status_labels[key] = status_label

    def toggle_api_visibility(self, key, entry):
        """Toggle API key visibility"""
        current_show = entry.cget('show')
        if current_show == '*':
            entry.config(show='')
        else:
            entry.config(show='*')

    def create_preferences_tab(self):
        """Create preferences tab"""
        pref_frame = tk.Frame(self.notebook, bg=ModernColors.BG_PRIMARY)
        self.notebook.add(pref_frame, text="⚙️ Preferences")

        # Scrollable content
        canvas = tk.Canvas(pref_frame, bg=ModernColors.BG_PRIMARY, highlightthickness=0)
        scrollbar = ttk.Scrollbar(pref_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernColors.BG_PRIMARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Preferences entries
        self.pref_vars = {}

        # General preferences
        general_card = ModernCard(scrollable_frame, title="General Settings")
        general_card.pack(fill=tk.X, padx=20, pady=(20, 10))

        # End pause duration
        self.create_scale_setting(general_card, "end_pause_duration",
                                 "End Pause Duration (seconds)", 0.0, 10.0, 0.5)

        # Default providers
        providers_card = ModernCard(scrollable_frame, title="Default Providers")
        providers_card.pack(fill=tk.X, padx=20, pady=10)

        self.create_dropdown_setting(providers_card, "default_ai_provider",
                                    "Default AI Provider", AI_PROVIDERS)

        self.create_dropdown_setting(providers_card, "default_tts_provider",
                                    "Default TTS Provider", TTS_PROVIDERS)

        # Video settings
        video_card = ModernCard(scrollable_frame, title="Video Defaults")
        video_card.pack(fill=tk.X, padx=20, pady=(10, 20))

        self.create_dropdown_setting(video_card, "default_video_quality",
                                    "Default Video Quality", ["720p", "1080p", "2K", "4K"])

        self.create_dropdown_setting(video_card, "default_orientation",
                                    "Default Orientation", ["portrait", "landscape"])

    def create_scale_setting(self, parent, key, label, from_val, to_val, resolution):
        """Create a scale setting widget"""
        setting_frame = tk.Frame(parent, bg=ModernColors.SURFACE)
        setting_frame.pack(fill=tk.X, pady=10)

        # Label
        tk.Label(
            setting_frame,
            text=f"{label}:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).pack(anchor="w", pady=(0, 5))

        # Scale
        var = tk.DoubleVar(value=self.preferences.get(key, from_val))
        self.pref_vars[key] = var

        scale = tk.Scale(
            setting_frame,
            from_=from_val,
            to=to_val,
            resolution=resolution,
            orient=tk.HORIZONTAL,
            variable=var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        scale.pack(fill=tk.X)

    def create_dropdown_setting(self, parent, key, label, options):
        """Create a dropdown setting widget"""
        setting_frame = tk.Frame(parent, bg=ModernColors.SURFACE)
        setting_frame.pack(fill=tk.X, pady=10)

        # Label
        tk.Label(
            setting_frame,
            text=f"{label}:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).pack(anchor="w", pady=(0, 5))

        # Dropdown
        var = tk.StringVar(value=self.preferences.get(key, options[0] if options else ""))
        self.pref_vars[key] = var

        dropdown = ttk.Combobox(
            setting_frame,
            textvariable=var,
            values=options,
            state="readonly",
            style="Modern.TCombobox"
        )
        dropdown.pack(fill=tk.X)

    def create_advanced_tab(self):
        """Create advanced settings tab"""
        adv_frame = tk.Frame(self.notebook, bg=ModernColors.BG_PRIMARY)
        self.notebook.add(adv_frame, text="🔧 Advanced")

        # Content
        content_frame = tk.Frame(adv_frame, bg=ModernColors.BG_PRIMARY)
        content_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Configuration info
        config_card = ModernCard(content_frame, title="Configuration Information")
        config_card.pack(fill=tk.X, pady=(0, 20))

        info_text = tk.Text(
            config_card,
            height=10,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        info_text.pack(fill=tk.BOTH, expand=True, pady=10)

        # Load config info
        try:
            config_info = f"Configuration File: config.json\n\n"
            config_info += f"Current Settings:\n"
            config_info += f"- OpenAI Model: {config.get('openai', {}).get('model', 'N/A')}\n"
            config_info += f"- Temperature: {config.get('openai', {}).get('temperature', 'N/A')}\n"
            config_info += f"- Max Scenes: {config.get('storyboard', {}).get('max_scenes', 'N/A')}\n"
            config_info += f"- Character Limits: {config.get('story_generation', {}).get('char_limit_min', 'N/A')}-{config.get('story_generation', {}).get('char_limit_max', 'N/A')}\n"

            info_text.config(state=tk.NORMAL)
            info_text.insert(tk.END, config_info)
            info_text.config(state=tk.DISABLED)
        except Exception as e:
            info_text.config(state=tk.NORMAL)
            info_text.insert(tk.END, f"Error loading configuration: {e}")
            info_text.config(state=tk.DISABLED)

    def save_settings(self):
        """Save all settings"""
        try:
            # Track if ElevenLabs API key was updated
            elevenlabs_key_updated = False
            old_elevenlabs_key = os.getenv('ELEVENLABS_API_KEY', '')

            # Save API keys to environment (would need to update .env file)
            env_updates = []
            for key, var in self.api_entries.items():
                value = var.get().strip()
                if value:
                    env_key = f"{key.upper()}_API_KEY"
                    env_updates.append(f"{env_key}={value}")
                    # Update current environment
                    os.environ[env_key] = value

                    # Check if ElevenLabs key was updated
                    if key == "elevenlabs" and value != old_elevenlabs_key:
                        elevenlabs_key_updated = True

            # Update .env file
            if env_updates:
                self.update_env_file(env_updates)

            # Apply preferences to app
            for key, var in self.pref_vars.items():
                value = var.get()
                if key == "end_pause_duration":
                    self.app.end_pause_var.set(value)
                elif key == "default_ai_provider":
                    self.app.ai_provider_var.set(value)
                elif key == "default_tts_provider":
                    self.app.tts_model_var.set(value)
                elif key == "default_image_model":
                    self.app.image_model_var.set(value)
                elif key == "default_video_quality":
                    self.app.video_quality_var.set(value)
                elif key == "default_orientation":
                    self.app.orientation_var.set(value)

            # Refresh ElevenLabs voices if API key was updated
            if elevenlabs_key_updated:
                # Reinitialize ElevenLabs client with new key
                try:
                    from elevenlabs_client import elevenlabs_client
                    elevenlabs_client.__init__()  # Reinitialize with new API key

                    # If currently on ElevenLabs TTS model, reload voices
                    if self.app.tts_model_var.get() == "ElevenLabs":
                        self.app.load_elevenlabs_voices()

                except Exception as e:
                    print(f"Error refreshing ElevenLabs voices: {e}")

            messagebox.showinfo("Settings Saved", "Settings have been saved successfully!")
            self.window.destroy()

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save settings: {e}")

    def update_env_file(self, updates):
        """Update the .env file with new API keys"""
        env_path = os.path.join(os.path.dirname(script_dir), ".env")

        # Read existing content
        existing_lines = []
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                existing_lines = f.readlines()

        # Update or add new keys
        updated_lines = []
        updated_keys = set()

        for line in existing_lines:
            line = line.strip()
            if '=' in line and not line.startswith('#'):
                key = line.split('=')[0]
                # Check if this key needs updating
                updated = False
                for update in updates:
                    if update.startswith(f"{key}="):
                        updated_lines.append(update + '\n')
                        updated_keys.add(key)
                        updated = True
                        break
                if not updated:
                    updated_lines.append(line + '\n')
            else:
                updated_lines.append(line + '\n')

        # Add new keys that weren't in the file
        for update in updates:
            key = update.split('=')[0]
            if key not in updated_keys:
                updated_lines.append(update + '\n')

        # Write back to file
        with open(env_path, 'w') as f:
            f.writelines(updated_lines)

    def test_apis(self):
        """Test API connections"""
        test_window = tk.Toplevel(self.window)
        test_window.title("API Test Results")
        test_window.geometry("600x400")
        test_window.configure(bg=ModernColors.BG_PRIMARY)
        test_window.transient(self.window)

        # Test results display
        results_text = tk.Text(
            test_window,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            wrap=tk.WORD
        )
        results_text.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        results_text.insert(tk.END, "Testing API connections...\n\n")
        test_window.update()

        # Test each API
        for key, var in self.api_entries.items():
            api_key = var.get().strip()
            if api_key:
                results_text.insert(tk.END, f"Testing {key.title()} API... ")
                test_window.update()

                try:
                    # Simple test based on API type
                    if key == "openai":
                        # Test OpenAI
                        import ai_client
                        os.environ['OPENAI_API_KEY'] = api_key
                        ai_client.initialize_clients()
                        if ai_client.openai_client:
                            results_text.insert(tk.END, "✅ Success\n")
                        else:
                            results_text.insert(tk.END, "❌ Failed\n")
                    else:
                        # For other APIs, just check if key format looks valid
                        if len(api_key) > 10:
                            results_text.insert(tk.END, "✅ Key format valid\n")
                        else:
                            results_text.insert(tk.END, "❌ Key too short\n")

                except Exception as e:
                    results_text.insert(tk.END, f"❌ Error: {e}\n")
            else:
                results_text.insert(tk.END, f"{key.title()} API: ⚠️ No key provided\n")

            test_window.update()

        results_text.insert(tk.END, "\nTesting complete!")

    def reset_settings(self):
        """Reset settings to defaults"""
        if messagebox.askyesno("Reset Settings", "Are you sure you want to reset all settings to defaults?"):
            # Reset API keys
            for var in self.api_entries.values():
                var.set("")

            # Reset preferences
            for key, var in self.pref_vars.items():
                if key == "end_pause_duration":
                    var.set(3.5)
                elif key == "default_ai_provider":
                    var.set("OpenAI")
                elif key == "default_tts_provider":
                    var.set("Voicely")
                elif key == "default_video_quality":
                    var.set("720p")
                elif key == "default_orientation":
                    var.set("portrait")

            # Update status labels
            for key, label in self.api_status_labels.items():
                label.config(text="❌ Not Set", fg=ModernColors.ERROR)

class ModernApp:
    """Modern 1ClickVideo Application with contemporary UI design"""

    def __init__(self, root):
        self.root = root

        # Initialize functionality first
        self.generator_thread = None
        self.is_processing = False
        self.log_messages = []
        self.most_recent_video = None

        self.setup_window()
        self.setup_variables()

        # Apply modern theme
        self.apply_modern_theme()

        self.setup_ui()

    def setup_window(self):
        """Configure the main window"""
        self.root.title(f"{APP_NAME} - Modern Interface")

        # Set window size and center it
        window_width = 1400
        window_height = 800

        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()

        center_x = int(screen_width/2 - window_width/2)
        center_y = int(screen_height/2 - window_height/2)

        self.root.geometry(f"{window_width}x{window_height}+{center_x}+{center_y}")
        self.root.minsize(1200, 700)
        self.root.configure(bg=ModernColors.BG_PRIMARY)

    def setup_variables(self):
        """Initialize all UI variables"""
        # Content options
        self.story_type_var = tk.StringVar(value="Fun Facts")
        self.image_style_var = tk.StringVar(value="Cinematic")
        self.image_model_var = tk.StringVar(value="Together AI Flux")
        self.ai_provider_var = tk.StringVar(value="OpenAI")

        # Voice options
        self.tts_model_var = tk.StringVar(value="Voicely")
        self.voice_var = tk.StringVar(value="en-US-AriaNeural")
        self.speech_rate_var = tk.DoubleVar(value=1.0)

        # Video settings
        self.video_quality_var = tk.StringVar(value="720p")
        self.orientation_var = tk.StringVar(value="portrait")
        self.bg_music_var = tk.StringVar(value="None")
        self.bg_music_volume_var = tk.DoubleVar(value=0.2)
        self.end_pause_var = tk.DoubleVar(value=3.5)

        # Subtitle settings
        self.font_var = tk.StringVar(value="TitanOne")
        self.font_color_var = tk.StringVar(value="#FFFFFF")
        self.font_size_var = tk.IntVar(value=40)
        self.outline_color_var = tk.StringVar(value="#000000")
        self.outline_size_var = tk.IntVar(value=3)
        self.caption_position_var = tk.StringVar(value="bottom")
        self.caption_words_var = tk.IntVar(value=3)
        self.highlight_words_var = tk.BooleanVar(value=True)
        self.highlight_color_var = tk.StringVar(value="#FFFF00")
        self.highlight_style_var = tk.StringVar(value="text_color")
        self.highlight_bg_color_var = tk.StringVar(value="#3700B3")
        self.highlight_bg_opacity_var = tk.DoubleVar(value=0.7)

        # Batch generation settings
        self.batch_enabled_var = tk.BooleanVar(value=False)
        self.batch_quantity_var = tk.StringVar(value="5")
        self.batch_custom_titles_var = tk.BooleanVar(value=False)
        self.titles_file_var = tk.StringVar()
        self.batch_titles = None
        self.batch_current = 1
        self.batch_total = 1

        # Custom content
        self.use_custom_title_var = tk.BooleanVar(value=False)
        self.custom_title_var = tk.StringVar()
        self.use_custom_script_var = tk.BooleanVar(value=False)
        self.custom_script_var = tk.StringVar()

        # Progress tracking
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value="Ready to generate video")

    def apply_modern_theme(self):
        """Apply modern theme to ttk widgets"""
        style = ttk.Style()

        # Configure modern styles
        style.configure("Modern.TFrame", background=ModernColors.BG_SECONDARY)
        style.configure("Modern.TLabel", background=ModernColors.BG_SECONDARY, foreground=ModernColors.TEXT_PRIMARY)
        style.configure("Modern.TButton", background=ModernColors.PRIMARY, foreground=ModernColors.TEXT_PRIMARY)
        style.configure("Modern.TEntry", fieldbackground=ModernColors.SURFACE, foreground=ModernColors.TEXT_PRIMARY)

        # Fix dropdown text color issues - ensure text is always visible
        style.configure("Modern.TCombobox",
                       fieldbackground=ModernColors.SURFACE,
                       foreground="#000000",  # Force black text for visibility
                       selectbackground=ModernColors.PRIMARY,
                       selectforeground="#FFFFFF",  # White text on colored background
                       insertcolor="#000000")  # Black cursor

        # Configure dropdown list styling with proper contrast
        style.map("Modern.TCombobox",
                 fieldbackground=[('readonly', ModernColors.SURFACE), ('active', ModernColors.SURFACE)],
                 selectbackground=[('readonly', ModernColors.PRIMARY), ('active', ModernColors.PRIMARY_HOVER)],
                 selectforeground=[('readonly', "#FFFFFF"), ('active', "#FFFFFF")],
                 foreground=[('readonly', "#000000"), ('active', "#000000"), ('focus', "#000000")])

        # Also configure the dropdown listbox
        self.root.option_add('*TCombobox*Listbox.foreground', '#000000')
        self.root.option_add('*TCombobox*Listbox.background', ModernColors.SURFACE)
        self.root.option_add('*TCombobox*Listbox.selectForeground', '#FFFFFF')
        self.root.option_add('*TCombobox*Listbox.selectBackground', ModernColors.PRIMARY)

    def setup_ui(self):
        """Create the modern user interface"""
        # Apply additional dropdown fixes after theme is set
        self.fix_dropdown_colors()

        # Main container
        main_container = tk.Frame(self.root, bg=ModernColors.BG_PRIMARY)
        main_container.pack(fill=tk.BOTH, expand=True)

        # Header
        self.create_header(main_container)

        # Tab Navigation
        self.create_tab_navigation(main_container)

        # Content area
        content_area = tk.Frame(main_container, bg=ModernColors.BG_PRIMARY)
        content_area.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Create tab content areas
        self.create_tab_content_areas(content_area)

        # Initialize with Video Generator tab - ensure it's visible
        self.current_tab = "video_generator"
        if "video_generator" in self.tab_content_areas:
            self.tab_content_areas["video_generator"].pack(fill=tk.BOTH, expand=True)

    def fix_dropdown_colors(self):
        """Additional fix for dropdown text visibility"""
        # Configure Tkinter options for better dropdown visibility
        self.root.option_add('*TCombobox*Listbox.foreground', 'black')
        self.root.option_add('*TCombobox*Listbox.background', 'white')
        self.root.option_add('*TCombobox*Listbox.selectForeground', 'white')
        self.root.option_add('*TCombobox*Listbox.selectBackground', ModernColors.PRIMARY)

        # Force combobox text to be black
        style = ttk.Style()
        style.configure("Modern.TCombobox",
                       foreground="black",
                       fieldbackground="white")
        style.map("Modern.TCombobox",
                 foreground=[('readonly', 'black'), ('active', 'black')],
                 fieldbackground=[('readonly', 'white'), ('active', 'white')])

    def create_header(self, parent):
        """Create modern header with branding and quick actions"""
        header = tk.Frame(parent, bg=ModernColors.BG_SECONDARY, height=80)
        header.pack(fill=tk.X, padx=0, pady=0)
        header.pack_propagate(False)

        # Header content
        header_content = tk.Frame(header, bg=ModernColors.BG_SECONDARY)
        header_content.pack(fill=tk.BOTH, expand=True, padx=30, pady=20)

        # Left side - Logo and title
        left_section = tk.Frame(header_content, bg=ModernColors.BG_SECONDARY)
        left_section.pack(side=tk.LEFT)

        # App logo
        logo_label = tk.Label(
            left_section,
            text="🎬",
            font=(DEFAULT_FONT, 28),
            fg=ModernColors.PRIMARY,
            bg=ModernColors.BG_SECONDARY
        )
        logo_label.pack(side=tk.LEFT, padx=(0, 15))

        # Title section
        title_frame = tk.Frame(left_section, bg=ModernColors.BG_SECONDARY)
        title_frame.pack(side=tk.LEFT)

        app_title = tk.Label(
            title_frame,
            text=APP_NAME,
            font=(DEFAULT_FONT, 24, "bold"),
            fg=ModernColors.TEXT_PRIMARY,
            bg=ModernColors.BG_SECONDARY
        )
        app_title.pack(anchor="w")

        subtitle = tk.Label(
            title_frame,
            text="Modern AI Video Creation",
            font=(DEFAULT_FONT, 12),
            fg=ModernColors.TEXT_SECONDARY,
            bg=ModernColors.BG_SECONDARY
        )
        subtitle.pack(anchor="w")

        # Right side - Quick actions
        right_section = tk.Frame(header_content, bg=ModernColors.BG_SECONDARY)
        right_section.pack(side=tk.RIGHT)

        # User info and logout
        current_user = get_current_user_email()
        if current_user:
            # User info section
            user_frame = tk.Frame(right_section, bg=ModernColors.BG_SECONDARY)
            user_frame.pack(side=tk.RIGHT, padx=(20, 0))

            user_label = tk.Label(
                user_frame,
                text=f"👤 {current_user[:20]}{'...' if len(current_user) > 20 else ''}",
                font=(DEFAULT_FONT, 10),
                fg=ModernColors.TEXT_SECONDARY,
                bg=ModernColors.BG_SECONDARY
            )
            user_label.pack(side=tk.TOP)

            # Logout button
            logout_btn = ModernButton(
                user_frame,
                text="🚪 Logout",
                command=self.logout,
                style="ghost",
                width=8
            )
            logout_btn.pack(side=tk.TOP, pady=(5, 0))

        # Version badge
        version_label = tk.Label(
            right_section,
            text=f"v{APP_VERSION}",
            font=(DEFAULT_FONT, 10, "bold"),
            fg=ModernColors.TEXT_PRIMARY,
            bg=ModernColors.PRIMARY,
            padx=8,
            pady=4
        )
        version_label.pack(side=tk.RIGHT, padx=(10, 0))

    def create_tab_navigation(self, parent):
        """Create horizontal tab navigation bar"""
        # Tab navigation container
        tab_nav_container = tk.Frame(parent, bg=ModernColors.BG_SECONDARY, height=60)
        tab_nav_container.pack(fill=tk.X, padx=0, pady=0)
        tab_nav_container.pack_propagate(False)

        # Tab navigation content
        tab_nav_content = tk.Frame(tab_nav_container, bg=ModernColors.BG_SECONDARY)
        tab_nav_content.pack(fill=tk.BOTH, expand=True, padx=30, pady=10)

        # Tab data: (tab_id, icon, label, tooltip)
        self.tab_data = [
            ("video_generator", "🎬", "Video Generator", "Generate AI videos with custom content"),
            ("subtitle_generator", "📝", "Subtitle Generator", "Add subtitles to existing videos"),
            ("script_generator", "✍️", "Script Generator", "Generate scripts for video content"),
            ("about", "ℹ️", "About", "About 1ClickVideo application")
        ]

        # Current active tab
        self.current_tab = "video_generator"
        self.tab_buttons = {}

        # Create tab buttons
        for tab_id, icon, label, tooltip in self.tab_data:
            # Determine initial style based on whether this is the current tab
            is_active = tab_id == self.current_tab

            tab_btn = ModernButton(
                tab_nav_content,
                text=f"{icon} {label}",
                command=lambda t=tab_id: self.switch_tab(t),
                style="primary" if is_active else "ghost",
                width=18
            )

            # Set initial colors manually to ensure proper appearance
            if is_active:
                tab_btn.configure(
                    bg=ModernColors.PRIMARY,
                    fg=ModernColors.TEXT_PRIMARY,
                    activebackground=ModernColors.PRIMARY_HOVER
                )
            else:
                tab_btn.configure(
                    bg=ModernColors.BG_SECONDARY,
                    fg=ModernColors.TEXT_SECONDARY,
                    activebackground=ModernColors.SURFACE_HOVER
                )

            tab_btn.pack(side=tk.LEFT, padx=(0, 10))
            self.tab_buttons[tab_id] = tab_btn

            # Add tooltip (simple implementation)
            self.create_tooltip(tab_btn, tooltip)

    def create_tooltip(self, widget, text):
        """Create a simple tooltip for a widget"""
        def on_enter(event):
            tooltip = tk.Toplevel()
            tooltip.wm_overrideredirect(True)
            tooltip.wm_geometry(f"+{event.x_root+10}+{event.y_root+10}")
            tooltip.configure(bg=ModernColors.BG_TERTIARY)

            label = tk.Label(
                tooltip,
                text=text,
                bg=ModernColors.BG_TERTIARY,
                fg=ModernColors.TEXT_PRIMARY,
                font=(DEFAULT_FONT, 9),
                padx=8,
                pady=4
            )
            label.pack()

            widget.tooltip = tooltip

        def on_leave(event):
            if hasattr(widget, 'tooltip'):
                widget.tooltip.destroy()
                del widget.tooltip

        widget.bind("<Enter>", on_enter)
        widget.bind("<Leave>", on_leave)

    def switch_tab(self, tab_id):
        """Switch to the specified tab"""
        if tab_id == self.current_tab:
            return

        # Update button colors directly
        for tid, btn in self.tab_buttons.items():
            if tid == tab_id:
                # Active tab - primary colors
                btn.configure(
                    bg=ModernColors.PRIMARY,
                    fg=ModernColors.TEXT_PRIMARY,
                    activebackground=ModernColors.PRIMARY_HOVER
                )
            else:
                # Inactive tab - ghost colors
                btn.configure(
                    bg=ModernColors.BG_SECONDARY,
                    fg=ModernColors.TEXT_SECONDARY,
                    activebackground=ModernColors.SURFACE_HOVER
                )

        # Hide all tab content areas
        for content_area in self.tab_content_areas.values():
            content_area.pack_forget()

        # Show selected tab content
        if tab_id in self.tab_content_areas:
            self.tab_content_areas[tab_id].pack(fill=tk.BOTH, expand=True)

        self.current_tab = tab_id
        self.log_message(f"Switched to {tab_id.replace('_', ' ').title()} tab")

    def create_tab_content_areas(self, parent):
        """Create content areas for each tab"""
        self.tab_content_areas = {}

        # Video Generator tab (main functionality)
        video_gen_frame = tk.Frame(parent, bg=ModernColors.BG_PRIMARY)
        self.tab_content_areas["video_generator"] = video_gen_frame
        self.create_video_generator_content(video_gen_frame)

        # Subtitle Generator tab
        subtitle_gen_frame = tk.Frame(parent, bg=ModernColors.BG_PRIMARY)
        self.tab_content_areas["subtitle_generator"] = subtitle_gen_frame
        self.create_subtitle_generator_content(subtitle_gen_frame)

        # Script Generator tab
        script_gen_frame = tk.Frame(parent, bg=ModernColors.BG_PRIMARY)
        self.tab_content_areas["script_generator"] = script_gen_frame
        self.create_script_generator_content(script_gen_frame)

        # About tab
        about_frame = tk.Frame(parent, bg=ModernColors.BG_PRIMARY)
        self.tab_content_areas["about"] = about_frame
        self.create_about_content(about_frame)

    def create_video_generator_content(self, parent):
        """Create the main video generator content (existing functionality)"""
        # This is the existing main layout functionality
        self.create_main_layout(parent)

    def create_subtitle_generator_content(self, parent):
        """Create subtitle generator tab content"""
        # Create scrollable content area
        canvas = tk.Canvas(parent, bg=ModernColors.BG_PRIMARY, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernColors.BG_PRIMARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mouse wheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Header
        header_card = ModernCard(scrollable_frame, title="Subtitle Generator")
        header_card.pack(fill=tk.X, padx=20, pady=(20, 10))

        subtitle_desc = tk.Label(
            header_card,
            text="Add customized subtitles to your existing videos",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 11)
        )
        subtitle_desc.pack(anchor="w", pady=(0, 10))

        # Warning note
        warning_frame = tk.Frame(header_card, bg=ModernColors.SURFACE)
        warning_frame.pack(fill=tk.X, pady=(0, 10))

        warning_icon = tk.Label(
            warning_frame,
            text="⚠️",
            bg=ModernColors.SURFACE,
            fg=ModernColors.WARNING,
            font=(DEFAULT_FONT, 12)
        )
        warning_icon.pack(side=tk.LEFT, padx=(0, 5))

        warning_text = tk.Label(
            warning_frame,
            text="Only English language supported and maximum of 5 minute video supported",
            bg=ModernColors.SURFACE,
            fg=ModernColors.WARNING,
            font=(DEFAULT_FONT, 10)
        )
        warning_text.pack(side=tk.LEFT)

        # Main content grid
        content_grid = tk.Frame(scrollable_frame, bg=ModernColors.BG_PRIMARY)
        content_grid.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Configure grid weights
        content_grid.columnconfigure(0, weight=1)  # Video selection
        content_grid.columnconfigure(1, weight=2)  # Subtitle options

        # Video Selection Card
        video_card = ModernCard(content_grid, title="Video Selection")
        video_card.grid(row=0, column=0, sticky="nsew", padx=(0, 10), pady=(0, 10))

        # Video path variables
        self.subtitle_video_path_var = tk.StringVar()

        # Video file selection
        video_frame = tk.Frame(video_card, bg=ModernColors.SURFACE)
        video_frame.pack(fill=tk.X, pady=10)

        tk.Label(
            video_frame,
            text="Video File:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).pack(anchor="w", pady=(0, 5))

        video_input_frame = tk.Frame(video_frame, bg=ModernColors.SURFACE)
        video_input_frame.pack(fill=tk.X)

        self.subtitle_video_entry = tk.Entry(
            video_input_frame,
            textvariable=self.subtitle_video_path_var,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            state="readonly"
        )
        self.subtitle_video_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))

        browse_btn = ModernButton(
            video_input_frame,
            text="Browse",
            command=self.browse_subtitle_video,
            style="outline",
            width=10
        )
        browse_btn.pack(side=tk.RIGHT)

        # Subtitle Options Card
        subtitle_options_card = ModernCard(content_grid, title="Subtitle Options")
        subtitle_options_card.grid(row=0, column=1, sticky="nsew", padx=(10, 0), pady=(0, 10))

        # Initialize subtitle variables
        self.subtitle_font_var = tk.StringVar(value="TitanOne")
        self.subtitle_font_size_var = tk.IntVar(value=40)
        self.subtitle_font_color_var = tk.StringVar(value="#FFFFFF")
        self.subtitle_outline_color_var = tk.StringVar(value="#000000")
        self.subtitle_outline_size_var = tk.IntVar(value=3)
        self.subtitle_words_per_caption_var = tk.IntVar(value=3)
        self.subtitle_position_var = tk.StringVar(value="bottom")
        self.subtitle_highlight_style_var = tk.StringVar(value="text_color")
        self.subtitle_highlight_color_var = tk.StringVar(value="#FFFF00")
        self.subtitle_highlight_bg_color_var = tk.StringVar(value="#3700B3")
        self.subtitle_highlight_bg_opacity_var = tk.DoubleVar(value=0.7)

        # Create subtitle options grid
        options_grid = tk.Frame(subtitle_options_card, bg=ModernColors.SURFACE)
        options_grid.pack(fill=tk.BOTH, expand=True, pady=10)
        options_grid.columnconfigure(1, weight=1)

        # Font selection
        tk.Label(
            options_grid,
            text="Font:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=0, column=0, sticky="w", padx=(0, 15), pady=8)

        available_fonts = get_available_fonts()
        if not available_fonts:
            available_fonts = ["TitanOne", "Arial", "Roboto"]

        self.subtitle_font_combo = ttk.Combobox(
            options_grid,
            textvariable=self.subtitle_font_var,
            values=available_fonts,
            state="readonly",
            style="Modern.TCombobox"
        )
        self.subtitle_font_combo.grid(row=0, column=1, sticky="ew", pady=8)

        # Font size
        tk.Label(
            options_grid,
            text="Font Size:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=1, column=0, sticky="w", padx=(0, 15), pady=8)

        font_size_frame = tk.Frame(options_grid, bg=ModernColors.SURFACE)
        font_size_frame.grid(row=1, column=1, sticky="ew", pady=8)

        self.subtitle_font_size_scale = tk.Scale(
            font_size_frame,
            from_=20,
            to=300,
            orient=tk.HORIZONTAL,
            variable=self.subtitle_font_size_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.subtitle_font_size_scale.pack(fill=tk.X)

        # Font color
        tk.Label(
            options_grid,
            text="Font Color:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=2, column=0, sticky="w", padx=(0, 15), pady=8)

        self.subtitle_font_color_picker = ModernColorPicker(
            options_grid,
            initial_color="#FFFFFF"
        )
        self.subtitle_font_color_picker.grid(row=2, column=1, sticky="w", pady=8)

        # Outline color
        tk.Label(
            options_grid,
            text="Outline Color:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=3, column=0, sticky="w", padx=(0, 15), pady=8)

        self.subtitle_outline_color_picker = ModernColorPicker(
            options_grid,
            initial_color="#000000"
        )
        self.subtitle_outline_color_picker.grid(row=3, column=1, sticky="w", pady=8)

        # Outline size
        tk.Label(
            options_grid,
            text="Outline Size:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=4, column=0, sticky="w", padx=(0, 15), pady=8)

        outline_size_frame = tk.Frame(options_grid, bg=ModernColors.SURFACE)
        outline_size_frame.grid(row=4, column=1, sticky="ew", pady=8)

        self.subtitle_outline_size_scale = tk.Scale(
            outline_size_frame,
            from_=0,
            to=10,
            orient=tk.HORIZONTAL,
            variable=self.subtitle_outline_size_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.subtitle_outline_size_scale.pack(fill=tk.X)

        # Words per caption
        tk.Label(
            options_grid,
            text="Words per Caption:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=5, column=0, sticky="w", padx=(0, 15), pady=8)

        words_frame = tk.Frame(options_grid, bg=ModernColors.SURFACE)
        words_frame.grid(row=5, column=1, sticky="ew", pady=8)

        self.subtitle_words_scale = tk.Scale(
            words_frame,
            from_=1,
            to=10,
            orient=tk.HORIZONTAL,
            variable=self.subtitle_words_per_caption_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.subtitle_words_scale.pack(fill=tk.X)

        # Position
        tk.Label(
            options_grid,
            text="Position:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=6, column=0, sticky="w", padx=(0, 15), pady=8)

        self.subtitle_position_combo = ttk.Combobox(
            options_grid,
            textvariable=self.subtitle_position_var,
            values=["top", "center", "bottom"],
            state="readonly",
            style="Modern.TCombobox"
        )
        self.subtitle_position_combo.grid(row=6, column=1, sticky="ew", pady=8)

        # Highlight style
        tk.Label(
            options_grid,
            text="Highlight Style:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=7, column=0, sticky="w", padx=(0, 15), pady=8)

        self.subtitle_highlight_style_combo = ttk.Combobox(
            options_grid,
            textvariable=self.subtitle_highlight_style_var,
            values=["text_color", "background"],
            state="readonly",
            style="Modern.TCombobox"
        )
        self.subtitle_highlight_style_combo.grid(row=7, column=1, sticky="ew", pady=8)
        self.subtitle_highlight_style_combo.bind("<<ComboboxSelected>>", self.on_subtitle_highlight_style_change)

        # Highlight color
        tk.Label(
            options_grid,
            text="Highlight Color:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=8, column=0, sticky="w", padx=(0, 15), pady=8)

        self.subtitle_highlight_color_picker = ModernColorPicker(
            options_grid,
            initial_color="#FFFF00"
        )
        self.subtitle_highlight_color_picker.grid(row=8, column=1, sticky="w", pady=8)

        # Background highlight color (initially hidden)
        self.subtitle_bg_color_label = tk.Label(
            options_grid,
            text="Background Color:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        )
        self.subtitle_bg_color_label.grid(row=9, column=0, sticky="w", padx=(0, 15), pady=8)

        self.subtitle_highlight_bg_color_picker = ModernColorPicker(
            options_grid,
            initial_color="#3700B3"
        )
        self.subtitle_highlight_bg_color_picker.grid(row=9, column=1, sticky="w", pady=8)

        # Background opacity (initially hidden)
        self.subtitle_bg_opacity_label = tk.Label(
            options_grid,
            text="Background Opacity:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        )
        self.subtitle_bg_opacity_label.grid(row=10, column=0, sticky="w", padx=(0, 15), pady=8)

        bg_opacity_frame = tk.Frame(options_grid, bg=ModernColors.SURFACE)
        bg_opacity_frame.grid(row=10, column=1, sticky="ew", pady=8)

        self.subtitle_bg_opacity_scale = tk.Scale(
            bg_opacity_frame,
            from_=0.1,
            to=1.0,
            resolution=0.1,
            orient=tk.HORIZONTAL,
            variable=self.subtitle_highlight_bg_opacity_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.subtitle_bg_opacity_scale.pack(fill=tk.X)

        # Set initial visibility of background options
        self.on_subtitle_highlight_style_change()

        # Generation Controls
        controls_card = ModernCard(scrollable_frame, title="Generation Controls")
        controls_card.pack(fill=tk.X, padx=20, pady=10)

        # Progress bar
        self.subtitle_progress_var = tk.IntVar(value=0)
        self.subtitle_progress = ttk.Progressbar(
            controls_card,
            orient=tk.HORIZONTAL,
            mode='determinate',
            variable=self.subtitle_progress_var
        )
        self.subtitle_progress.pack(fill=tk.X, pady=(0, 10))

        # Status label
        self.subtitle_status_var = tk.StringVar(value="Ready to generate subtitles")
        self.subtitle_status_label = tk.Label(
            controls_card,
            textvariable=self.subtitle_status_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 10)
        )
        self.subtitle_status_label.pack(anchor="w", pady=(0, 10))

        # Generate button
        self.generate_subtitle_btn = ModernButton(
            controls_card,
            text="🎬 Generate Subtitles",
            command=self.generate_subtitles,
            style="primary",
            width=20
        )
        self.generate_subtitle_btn.pack(anchor="center")

        # Output section
        output_card = ModernCard(scrollable_frame, title="Output")
        output_card.pack(fill=tk.X, padx=20, pady=10)

        # Output path display
        self.subtitle_output_path_var = tk.StringVar()
        self.subtitle_output_label = tk.Label(
            output_card,
            textvariable=self.subtitle_output_path_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 10),
            wraplength=600
        )
        self.subtitle_output_label.pack(anchor="w", pady=(0, 10))

        # Output buttons
        output_buttons = tk.Frame(output_card, bg=ModernColors.SURFACE)
        output_buttons.pack(fill=tk.X)

        self.open_subtitle_video_btn = ModernButton(
            output_buttons,
            text="📹 Open Video",
            command=self.open_subtitle_video,
            style="outline",
            width=12,
            state=tk.DISABLED
        )
        self.open_subtitle_video_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.open_subtitle_folder_btn = ModernButton(
            output_buttons,
            text="📁 Open Folder",
            command=self.open_subtitle_folder,
            style="outline",
            width=12,
            state=tk.DISABLED
        )
        self.open_subtitle_folder_btn.pack(side=tk.LEFT)

        # Initialize subtitle generator thread
        self.subtitle_generator_thread = None

    def browse_subtitle_video(self):
        """Browse for video file for subtitle generation"""
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video Files", "*.mp4 *.avi *.mov *.mkv *.wmv *.flv *.webm"),
                ("All Files", "*.*")
            ]
        )

        if file_path:
            self.subtitle_video_path_var.set(file_path)
            self.log_message(f"Selected video for subtitles: {os.path.basename(file_path)}")

    def generate_subtitles(self):
        """Generate subtitles for the selected video"""
        video_path = self.subtitle_video_path_var.get()

        if not video_path:
            messagebox.showerror("Error", "Please select a video file first")
            return

        if not os.path.exists(video_path):
            messagebox.showerror("Error", "The selected video file does not exist")
            return

        # Check video length (basic check)
        try:
            # Import here to avoid dependency issues
            from moviepy.editor import VideoFileClip
            clip = VideoFileClip(video_path)
            duration_minutes = clip.duration / 60
            clip.close()

            if duration_minutes > 5:
                messagebox.showerror(
                    "Video Too Long",
                    f"The selected video is {duration_minutes:.1f} minutes long. " +
                    "Only videos up to 5 minutes are supported."
                )
                return
        except Exception as e:
            self.log_message(f"Could not check video duration: {str(e)}")
            # Continue anyway

        # Disable generate button and reset progress
        self.generate_subtitle_btn.configure(state=tk.DISABLED)
        self.subtitle_progress_var.set(0)
        self.subtitle_status_var.set("Preparing to generate subtitles...")

        # Import subtitle generator thread
        try:
            from subtitle_generator import SubtitleGeneratorThread

            # Create and start subtitle generation thread
            self.subtitle_generator_thread = SubtitleGeneratorThread(
                video_path=video_path,
                font_name=self.subtitle_font_var.get(),
                font_size=self.subtitle_font_size_var.get(),
                font_color=self.subtitle_font_color_picker.get_color(),
                outline_color=self.subtitle_outline_color_picker.get_color(),
                outline_size=self.subtitle_outline_size_var.get(),
                caption_words=self.subtitle_words_per_caption_var.get(),
                caption_position=self.subtitle_position_var.get(),
                highlight_style=self.subtitle_highlight_style_var.get(),
                highlight_color=self.subtitle_highlight_color_picker.get_color(),
                highlight_bg_color=self.subtitle_highlight_bg_color_picker.get_color(),
                highlight_bg_opacity=self.subtitle_highlight_bg_opacity_var.get(),
                callback=self.update_subtitle_status,
                update_progress=self.update_subtitle_progress
            )

            self.subtitle_generator_thread.start()

            # Start checking thread status
            self.root.after(100, self.check_subtitle_thread)

        except ImportError as e:
            self.log_message(f"Error importing subtitle generator: {str(e)}")
            messagebox.showerror("Error", "Subtitle generator module not found")
            self.generate_subtitle_btn.configure(state=tk.NORMAL)
        except Exception as e:
            self.log_message(f"Error starting subtitle generation: {str(e)}")
            messagebox.showerror("Error", f"Failed to start subtitle generation: {str(e)}")
            self.generate_subtitle_btn.configure(state=tk.NORMAL)

    def update_subtitle_status(self, message):
        """Update subtitle generation status"""
        self.subtitle_status_var.set(message)
        self.log_message(f"Subtitle: {message}")

    def update_subtitle_progress(self, progress):
        """Update subtitle generation progress"""
        self.subtitle_progress_var.set(progress)

    def check_subtitle_thread(self):
        """Check subtitle generation thread status"""
        if self.subtitle_generator_thread and self.subtitle_generator_thread.is_alive():
            # Thread is still running, check again later
            self.root.after(100, self.check_subtitle_thread)
        else:
            # Thread has finished
            if self.subtitle_generator_thread and hasattr(self.subtitle_generator_thread, 'result'):
                result = self.subtitle_generator_thread.result
                if result and result.get('success'):
                    self.log_message("Subtitle generation completed successfully!")
                    output_path = result.get('output_path')
                    if output_path:
                        self.log_message(f"Subtitled video saved: {output_path}")
                        # Update output display and enable buttons
                        self.subtitle_output_path_var.set(f"Output: {output_path}")
                        self.open_subtitle_video_btn.configure(state=tk.NORMAL)
                        self.open_subtitle_folder_btn.configure(state=tk.NORMAL)

                        # Offer to open the video
                        if messagebox.askyesno("Success",
                                             "Subtitles generated successfully!\n\n"
                                             "Would you like to open the video?"):
                            self.open_subtitle_video()
                else:
                    error_msg = result.get('message', 'Unknown error') if result else 'Unknown error'
                    self.log_message(f"Subtitle generation failed: {error_msg}")
                    messagebox.showerror("Error", f"Subtitle generation failed: {error_msg}")

            # Re-enable generate button
            self.generate_subtitle_btn.configure(state=tk.NORMAL)
            self.subtitle_progress_var.set(0)
            self.subtitle_status_var.set("Ready to generate subtitles")

    def on_subtitle_highlight_style_change(self, event=None):
        """Handle highlight style change for subtitle generator"""
        highlight_style = self.subtitle_highlight_style_var.get()

        if highlight_style == "background":
            # Show background options
            self.subtitle_bg_color_label.grid()
            self.subtitle_highlight_bg_color_picker.grid()
            self.subtitle_bg_opacity_label.grid()
            self.subtitle_bg_opacity_scale.master.grid()
        else:
            # Hide background options
            self.subtitle_bg_color_label.grid_remove()
            self.subtitle_highlight_bg_color_picker.grid_remove()
            self.subtitle_bg_opacity_label.grid_remove()
            self.subtitle_bg_opacity_scale.master.grid_remove()

    def open_subtitle_video(self):
        """Open the generated subtitled video"""
        if self.subtitle_generator_thread and hasattr(self.subtitle_generator_thread, 'result'):
            result = self.subtitle_generator_thread.result
            if result and result.get('success'):
                output_path = result.get('output_path')
                if output_path and os.path.exists(output_path):
                    try:
                        if platform.system() == "Windows":
                            os.startfile(output_path)
                        elif platform.system() == "Darwin":  # macOS
                            subprocess.run(["open", output_path])
                        else:  # Linux
                            subprocess.run(["xdg-open", output_path])
                    except Exception as e:
                        self.log_message(f"Could not open video: {str(e)}")
                        messagebox.showerror("Error", f"Could not open video: {str(e)}")
                else:
                    messagebox.showerror("Error", "Video file not found")
            else:
                messagebox.showerror("Error", "No video has been generated yet")

    def open_subtitle_folder(self):
        """Open the folder containing the generated subtitled video"""
        if self.subtitle_generator_thread and hasattr(self.subtitle_generator_thread, 'result'):
            result = self.subtitle_generator_thread.result
            if result and result.get('success'):
                output_path = result.get('output_path')
                if output_path and os.path.exists(output_path):
                    folder_path = os.path.dirname(output_path)
                    try:
                        if platform.system() == "Windows":
                            os.startfile(folder_path)
                        elif platform.system() == "Darwin":  # macOS
                            subprocess.run(["open", folder_path])
                        else:  # Linux
                            subprocess.run(["xdg-open", folder_path])
                    except Exception as e:
                        self.log_message(f"Could not open folder: {str(e)}")
                        messagebox.showerror("Error", f"Could not open folder: {str(e)}")
                else:
                    messagebox.showerror("Error", "Folder not found")
            else:
                messagebox.showerror("Error", "No video has been generated yet")

    def create_script_generator_content(self, parent):
        """Create script generator tab content"""
        # Create scrollable content area
        canvas = tk.Canvas(parent, bg=ModernColors.BG_PRIMARY, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernColors.BG_PRIMARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mouse wheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Header
        header_card = ModernCard(scrollable_frame, title="Script Generator")
        header_card.pack(fill=tk.X, padx=20, pady=(20, 10))

        script_desc = tk.Label(
            header_card,
            text="Generate engaging scripts for your video content using AI",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 11)
        )
        script_desc.pack(anchor="w", pady=(0, 10))

        # Main content grid
        content_grid = tk.Frame(scrollable_frame, bg=ModernColors.BG_PRIMARY)
        content_grid.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # Configure grid weights
        content_grid.columnconfigure(0, weight=1)  # Input options
        content_grid.columnconfigure(1, weight=2)  # Output area

        # Input Options Card
        input_card = ModernCard(content_grid, title="Script Configuration")
        input_card.grid(row=0, column=0, sticky="nsew", padx=(0, 10), pady=(0, 10))

        # Initialize script variables
        self.script_concept_var = tk.StringVar()
        self.script_type_var = tk.StringVar(value="Motivational")
        self.script_word_count_var = tk.IntVar(value=150)
        self.script_llm_provider_var = tk.StringVar(value="Groq")

        # Script input options
        input_grid = tk.Frame(input_card, bg=ModernColors.SURFACE)
        input_grid.pack(fill=tk.BOTH, expand=True, pady=10)
        input_grid.columnconfigure(0, weight=1)

        # Concept input
        tk.Label(
            input_grid,
            text="Script Concept:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11, "bold")
        ).grid(row=0, column=0, sticky="w", pady=(0, 5))

        self.script_concept_text = tk.Text(
            input_grid,
            height=4,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            wrap=tk.WORD
        )
        self.script_concept_text.grid(row=1, column=0, sticky="ew", pady=(0, 15))

        # Script type
        tk.Label(
            input_grid,
            text="Script Type:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=2, column=0, sticky="w", pady=(0, 5))

        # Import all story types and add Custom option
        from utils import STORY_TYPES
        script_types = list(STORY_TYPES) + ["Custom"]

        self.script_type_combo = ttk.Combobox(
            input_grid,
            textvariable=self.script_type_var,
            values=script_types,
            state="readonly",
            style="Modern.TCombobox"
        )
        self.script_type_combo.grid(row=3, column=0, sticky="ew", pady=(0, 15))

        # Bind event to show/hide custom options
        self.script_type_combo.bind("<<ComboboxSelected>>", self.on_script_type_change)

        # Custom script type options (initially hidden)
        self.custom_options_frame = tk.Frame(input_grid, bg=ModernColors.SURFACE)
        self.custom_options_frame.grid(row=4, column=0, sticky="ew", pady=(0, 15))
        self.custom_options_frame.grid_remove()  # Hide initially

        # Custom tone
        tk.Label(
            self.custom_options_frame,
            text="Custom Tone:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10, "bold")
        ).grid(row=0, column=0, sticky="w", pady=(5, 5), padx=(0, 10))

        self.custom_tone_var = tk.StringVar(value="Professional")
        custom_tone_combo = ttk.Combobox(
            self.custom_options_frame,
            textvariable=self.custom_tone_var,
            values=["Professional", "Casual", "Humorous", "Serious", "Inspirational", "Educational", "Conversational"],
            state="readonly",
            style="Modern.TCombobox",
            font=(DEFAULT_FONT, 9)
        )
        custom_tone_combo.grid(row=0, column=1, sticky="ew", pady=(5, 5))

        # Custom style
        tk.Label(
            self.custom_options_frame,
            text="Custom Style:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10, "bold")
        ).grid(row=1, column=0, sticky="w", pady=(5, 5), padx=(0, 10))

        self.custom_style_var = tk.StringVar(value="Narrative")
        custom_style_combo = ttk.Combobox(
            self.custom_options_frame,
            textvariable=self.custom_style_var,
            values=["Narrative", "List-based", "Question & Answer", "Tutorial", "Review", "Commentary", "Interview"],
            state="readonly",
            style="Modern.TCombobox",
            font=(DEFAULT_FONT, 9)
        )
        custom_style_combo.grid(row=1, column=1, sticky="ew", pady=(5, 5))

        # Custom target audience
        tk.Label(
            self.custom_options_frame,
            text="Target Audience:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10, "bold")
        ).grid(row=2, column=0, sticky="w", pady=(5, 5), padx=(0, 10))

        self.custom_audience_var = tk.StringVar(value="General")
        custom_audience_combo = ttk.Combobox(
            self.custom_options_frame,
            textvariable=self.custom_audience_var,
            values=["General", "Young Adults", "Professionals", "Students", "Parents", "Entrepreneurs", "Creatives"],
            state="readonly",
            style="Modern.TCombobox",
            font=(DEFAULT_FONT, 9)
        )
        custom_audience_combo.grid(row=2, column=1, sticky="ew", pady=(5, 5))

        # Configure custom options grid
        self.custom_options_frame.grid_columnconfigure(1, weight=1)

        # Word count
        tk.Label(
            input_grid,
            text="Word Count:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=5, column=0, sticky="w", pady=(0, 5))

        word_count_frame = tk.Frame(input_grid, bg=ModernColors.SURFACE)
        word_count_frame.grid(row=6, column=0, sticky="ew", pady=(0, 15))

        self.script_word_count_scale = tk.Scale(
            word_count_frame,
            from_=50,
            to=500,
            orient=tk.HORIZONTAL,
            variable=self.script_word_count_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY,
            command=self.update_script_word_count_label
        )
        self.script_word_count_scale.pack(side=tk.LEFT, fill=tk.X, expand=True)

        # Word count label
        self.script_word_count_label = tk.Label(
            word_count_frame,
            text="150 words",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 10)
        )
        self.script_word_count_label.pack(side=tk.RIGHT, padx=(10, 0))

        # LLM Provider
        tk.Label(
            input_grid,
            text="AI Provider:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=7, column=0, sticky="w", pady=(0, 5))

        provider_frame = tk.Frame(input_grid, bg=ModernColors.SURFACE)
        provider_frame.grid(row=8, column=0, sticky="ew", pady=(0, 15))

        for provider in AI_PROVIDERS:
            rb = tk.Radiobutton(
                provider_frame,
                text=provider,
                variable=self.script_llm_provider_var,
                value=provider,
                bg=ModernColors.SURFACE,
                fg=ModernColors.TEXT_PRIMARY,
                selectcolor=ModernColors.PRIMARY,
                activebackground=ModernColors.SURFACE,
                font=(DEFAULT_FONT, 10)
            )
            rb.pack(anchor="w", pady=2)

        # Generate button
        self.generate_script_btn = ModernButton(
            input_grid,
            text="✍️ Generate Script",
            command=self.generate_script,
            style="primary",
            width=18
        )
        self.generate_script_btn.grid(row=9, column=0, pady=(10, 0))

        # Output Area Card
        output_card = ModernCard(content_grid, title="Generated Script")
        output_card.grid(row=0, column=1, sticky="nsew", padx=(10, 0), pady=(0, 10))

        # Script output text area
        self.script_output_text = tk.Text(
            output_card,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11),
            relief=tk.FLAT,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.script_output_text.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # Output controls
        output_controls = tk.Frame(output_card, bg=ModernColors.SURFACE)
        output_controls.pack(fill=tk.X)

        self.copy_script_btn = ModernButton(
            output_controls,
            text="📋 Copy Script",
            command=self.copy_script,
            style="outline",
            width=12,
            state=tk.DISABLED
        )
        self.copy_script_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.use_script_btn = ModernButton(
            output_controls,
            text="🎬 Use in Video Generator",
            command=self.use_script_in_video_generator,
            style="secondary",
            width=20,
            state=tk.DISABLED
        )
        self.use_script_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.save_script_btn = ModernButton(
            output_controls,
            text="💾 Save Script",
            command=self.save_script,
            style="outline",
            width=12,
            state=tk.DISABLED
        )
        self.save_script_btn.pack(side=tk.RIGHT)

        # Progress and Status
        status_card = ModernCard(scrollable_frame, title="Generation Status")
        status_card.pack(fill=tk.X, padx=20, pady=10)

        # Progress bar
        self.script_progress_var = tk.IntVar(value=0)
        self.script_progress = ttk.Progressbar(
            status_card,
            orient=tk.HORIZONTAL,
            mode='determinate',
            variable=self.script_progress_var
        )
        self.script_progress.pack(fill=tk.X, pady=(0, 10))

        # Status label
        self.script_status_var = tk.StringVar(value="Ready to generate script")
        self.script_status_label = tk.Label(
            status_card,
            textvariable=self.script_status_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 10)
        )
        self.script_status_label.pack(anchor="w")

        # Initialize script generator thread
        self.script_generator_thread = None

    def on_script_type_change(self, event=None):
        """Handle script type change to show/hide custom options and update word count"""
        script_type = self.script_type_var.get()

        # Show/hide custom options based on script type
        if script_type == "Custom":
            self.custom_options_frame.grid()
        else:
            self.custom_options_frame.grid_remove()

        # Update word count based on script type (same logic as main.py)
        default_word_counts = {
            "Motivational": 150,
            "Funny": 120,
            "Educational": 200,
            "Storytelling": 250,
            "Philosophy": 180,
            "Fun Facts": 150,
            "Scary": 180,
            "Mystery": 200,
            "Bedtime": 150,
            "Interesting History": 200,
            "Urban Legends": 180,
            "Long Form Jokes": 120,
            "Life Pro Tips": 150,
            "Love": 150,
            "AITA Stories": 250,
            "Storytime": 250,
            "POV": 150,
            "Day in the Life": 200,
            "True Crime": 250,
            "Celebrity Facts": 150,
            "Conspiracy Theories": 200,
            "Money Saving Tips": 150,
            "Fitness Hacks": 150,
            "Psychology Facts": 150,
            "Product Reviews": 200,
            "Travel Guides": 200,
            "DIY Tutorials": 200,
            "Cooking Tips": 150,
            "Dating Advice": 150,
            "Pet Tips": 150,
            "Islamic": 180,
            "Custom": 150  # Default for custom
        }

        # Set the word count based on script type
        if script_type in default_word_counts:
            self.script_word_count_var.set(default_word_counts[script_type])
        else:
            # Default for any other script type
            self.script_word_count_var.set(150)

        # Update the word count label
        self.update_script_word_count_label()

    def update_script_word_count_label(self, value=None):
        """Update word count label"""
        word_count = int(self.script_word_count_var.get())
        self.script_word_count_label.config(text=f"{word_count} words")

    def generate_script(self):
        """Generate script based on user input"""
        # Get concept from text widget
        concept = self.script_concept_text.get("1.0", tk.END).strip()

        if not concept:
            messagebox.showerror("Error", "Please enter a script concept")
            return

        # Get other parameters
        script_type = self.script_type_var.get()
        word_count = self.script_word_count_var.get()
        llm_provider = self.script_llm_provider_var.get()

        # For custom script type, get additional parameters
        if script_type == "Custom":
            custom_tone = self.custom_tone_var.get()
            custom_style = self.custom_style_var.get()
            custom_audience = self.custom_audience_var.get()

            # Create a custom script type description
            script_type = f"Custom ({custom_tone}, {custom_style}, for {custom_audience})"

        # Switch AI provider if needed
        current_provider = ai_client.get_current_provider()
        if current_provider != llm_provider:
            success, message = switch_ai_provider(llm_provider)
            if not success:
                messagebox.showerror("Error", f"Failed to switch to {llm_provider}: {message}")
                return

        # Disable generate button and reset progress
        self.generate_script_btn.configure(state=tk.DISABLED)
        self.script_progress_var.set(0)
        self.script_status_var.set("Preparing to generate script...")

        # Clear previous output
        self.script_output_text.configure(state=tk.NORMAL)
        self.script_output_text.delete("1.0", tk.END)
        self.script_output_text.configure(state=tk.DISABLED)

        # Import and start script generation
        try:
            from script_generator import generate_script as gen_script

            self.script_generator_thread = gen_script(
                concept=concept,
                script_type=script_type,
                callback=self.update_script_status,
                update_progress=self.update_script_progress,
                llm_provider=llm_provider,
                word_count=word_count
            )

            # Start checking thread status
            self.root.after(100, self.check_script_thread)

        except ImportError as e:
            self.log_message(f"Error importing script generator: {str(e)}")
            messagebox.showerror("Error", "Script generator module not found")
            self.generate_script_btn.configure(state=tk.NORMAL)
        except Exception as e:
            self.log_message(f"Error starting script generation: {str(e)}")
            messagebox.showerror("Error", f"Failed to start script generation: {str(e)}")
            self.generate_script_btn.configure(state=tk.NORMAL)

    def update_script_status(self, message):
        """Update script generation status"""
        self.script_status_var.set(message)
        self.log_message(f"Script: {message}")

    def update_script_progress(self, progress):
        """Update script generation progress"""
        self.script_progress_var.set(progress)

    def check_script_thread(self):
        """Check script generation thread status"""
        if self.script_generator_thread and self.script_generator_thread.is_alive():
            # Thread is still running, check again later
            self.root.after(100, self.check_script_thread)
        else:
            # Thread has finished
            if self.script_generator_thread and hasattr(self.script_generator_thread, 'result'):
                result = self.script_generator_thread.result
                if result and result.get('success'):
                    script_content = result.get('script', '')
                    if script_content:
                        # Display the generated script
                        self.script_output_text.configure(state=tk.NORMAL)
                        self.script_output_text.delete("1.0", tk.END)
                        self.script_output_text.insert("1.0", script_content)
                        self.script_output_text.configure(state=tk.DISABLED)

                        # Enable action buttons
                        self.copy_script_btn.configure(state=tk.NORMAL)
                        self.use_script_btn.configure(state=tk.NORMAL)
                        self.save_script_btn.configure(state=tk.NORMAL)

                        self.log_message("Script generation completed successfully!")
                    else:
                        self.log_message("Script generation completed but no content received")
                        messagebox.showerror("Error", "No script content was generated")
                else:
                    error_msg = result.get('message', 'Unknown error') if result else 'Unknown error'
                    self.log_message(f"Script generation failed: {error_msg}")
                    messagebox.showerror("Error", f"Script generation failed: {error_msg}")

            # Re-enable generate button
            self.generate_script_btn.configure(state=tk.NORMAL)
            self.script_progress_var.set(0)
            self.script_status_var.set("Ready to generate script")

    def copy_script(self):
        """Copy generated script to clipboard"""
        script_content = self.script_output_text.get("1.0", tk.END).strip()
        if script_content:
            self.root.clipboard_clear()
            self.root.clipboard_append(script_content)
            self.log_message("Script copied to clipboard")
            messagebox.showinfo("Success", "Script copied to clipboard!")

    def use_script_in_video_generator(self):
        """Use generated script in the video generator tab"""
        script_content = self.script_output_text.get("1.0", tk.END).strip()
        if script_content:
            # Switch to video generator tab
            self.switch_tab("video_generator")

            # Set the custom script in the video generator
            if hasattr(self, 'custom_script_text'):
                self.custom_script_text.delete("1.0", tk.END)
                self.custom_script_text.insert("1.0", script_content)
                self.custom_script_var.set(True)  # Enable custom script checkbox
                self.log_message("Script transferred to Video Generator")
                messagebox.showinfo("Success", "Script has been transferred to the Video Generator tab!")

    def save_script(self):
        """Save generated script to file"""
        script_content = self.script_output_text.get("1.0", tk.END).strip()
        if not script_content:
            messagebox.showwarning("No Content", "No script content to save")
            return

        # Ask user for save location
        file_path = filedialog.asksaveasfilename(
            title="Save Script",
            defaultextension=".txt",
            filetypes=[
                ("Text files", "*.txt"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            try:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(script_content)
                self.log_message(f"Script saved to {file_path}")
                messagebox.showinfo("Success", f"Script saved to {file_path}")
            except Exception as e:
                self.log_message(f"Error saving script: {str(e)}")
                messagebox.showerror("Error", f"Could not save the script: {str(e)}")

    def create_about_content(self, parent):
        """Create about tab content"""
        # Create scrollable content area
        canvas = tk.Canvas(parent, bg=ModernColors.BG_PRIMARY, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernColors.BG_PRIMARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mouse wheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Header
        header_card = ModernCard(scrollable_frame, title="About 1ClickVideo")
        header_card.pack(fill=tk.X, padx=20, pady=(20, 10))

        # Developer image and info section
        dev_section = tk.Frame(header_card, bg=ModernColors.SURFACE)
        dev_section.pack(fill=tk.X, pady=10)

        # Try to load and display the developer image
        try:
            from PIL import Image, ImageTk
            # Load and resize the image
            img = Image.open("ghalib.jpg")
            # Resize to a reasonable size for the About section
            img = img.resize((120, 120), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            # Create image label
            img_label = tk.Label(
                dev_section,
                image=photo,
                bg=ModernColors.SURFACE
            )
            img_label.image = photo  # Keep a reference
            img_label.pack(side=tk.LEFT, padx=(0, 20), pady=10)
        except Exception as e:
            # If image loading fails, show a placeholder
            placeholder_label = tk.Label(
                dev_section,
                text="👨‍💻",
                font=(DEFAULT_FONT, 48),
                bg=ModernColors.SURFACE,
                fg=ModernColors.TEXT_PRIMARY
            )
            placeholder_label.pack(side=tk.LEFT, padx=(0, 20), pady=10)

        # App and developer info
        info_section = tk.Frame(dev_section, bg=ModernColors.SURFACE)
        info_section.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        app_info = f"""
{APP_NAME} - Modern AI Video Creation Platform

Version: {APP_VERSION}
Interface: Modern UI with Material Design

This application provides a comprehensive suite of tools for creating AI-powered videos with professional quality and modern design.
        """.strip()

        info_label = tk.Label(
            info_section,
            text=app_info,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11),
            justify=tk.LEFT
        )
        info_label.pack(anchor="w", pady=(0, 15))

        # Developer information
        dev_info_card = ModernCard(scrollable_frame, title="Developer Information")
        dev_info_card.pack(fill=tk.X, padx=20, pady=10)

        dev_info = """
👨‍💻 Developer: MD Galib Hasan
📧 Professional AI Video Creation Solutions
🚀 Specialized in Modern UI/UX Design

For support, inquiries, or custom development:
        """.strip()

        dev_info_label = tk.Label(
            dev_info_card,
            text=dev_info,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11),
            justify=tk.LEFT
        )
        dev_info_label.pack(anchor="w", pady=(0, 15))

        # WhatsApp contact button
        whatsapp_frame = tk.Frame(dev_info_card, bg=ModernColors.SURFACE)
        whatsapp_frame.pack(fill=tk.X)

        whatsapp_btn = ModernButton(
            whatsapp_frame,
            text="💬 Contact via WhatsApp",
            command=self.open_whatsapp_contact,
            style="primary",
            width=20
        )
        whatsapp_btn.pack(side=tk.LEFT)

        # Features card
        features_card = ModernCard(scrollable_frame, title="Features")
        features_card.pack(fill=tk.X, padx=20, pady=10)

        features_text = """
🎬 Video Generator
• Generate AI videos with custom content
• Multiple story types and image styles
• Professional subtitle styling
• Background music integration

📝 Subtitle Generator
• Add subtitles to existing videos
• Customizable fonts and styling
• Multiple positioning options
• Automatic transcription

✍️ Script Generator
• AI-powered script creation
• Multiple script types
• Customizable word count
• Integration with video generator

⚙️ Advanced Features
• Multiple AI providers (OpenAI, Groq)
• Multiple image generators (Replicate, FAL AI, Together AI)
• Multiple TTS providers (OpenAI, Voicely, ElevenLabs)
• Real-time progress tracking
        """.strip()

        features_label = tk.Label(
            features_card,
            text=features_text,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            justify=tk.LEFT
        )
        features_label.pack(anchor="w", pady=10)

    def open_whatsapp_contact(self):
        """Open WhatsApp contact for the developer"""
        import webbrowser
        # Replace with the actual WhatsApp number
        whatsapp_number = "**********"  # Replace with actual number
        message = "Hi! I'm interested in 1ClickVideo and would like to get in touch."
        whatsapp_url = f"https://wa.me/{whatsapp_number}?text={message}"

        try:
            webbrowser.open(whatsapp_url)
            self.log_message("Opening WhatsApp contact...")
        except Exception as e:
            self.log_message(f"Could not open WhatsApp: {str(e)}")
            # Fallback - copy number to clipboard
            self.root.clipboard_clear()
            self.root.clipboard_append(whatsapp_number)
            messagebox.showinfo(
                "WhatsApp Contact",
                f"WhatsApp number copied to clipboard: {whatsapp_number}\n\n"
                "You can manually open WhatsApp and paste this number to contact the developer."
            )

    def create_main_layout(self, parent):
        """Create the main content layout with modern cards"""
        # Create scrollable content area
        canvas = tk.Canvas(parent, bg=ModernColors.BG_PRIMARY, highlightthickness=0)
        scrollbar = ttk.Scrollbar(parent, orient="vertical", command=canvas.yview)
        scrollable_frame = tk.Frame(canvas, bg=ModernColors.BG_PRIMARY)

        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )

        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)

        # Pack scrollable components
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")

        # Bind mouse wheel
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)

        # Create main content grid
        content_grid = tk.Frame(scrollable_frame, bg=ModernColors.BG_PRIMARY)
        content_grid.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

        # Configure grid weights
        content_grid.columnconfigure(0, weight=2)  # Left column (wider)
        content_grid.columnconfigure(1, weight=1)  # Right column

        # Left column - Main options
        left_column = tk.Frame(content_grid, bg=ModernColors.BG_PRIMARY)
        left_column.grid(row=0, column=0, sticky="nsew", padx=(0, 10))

        # Right column - Status and controls
        right_column = tk.Frame(content_grid, bg=ModernColors.BG_PRIMARY)
        right_column.grid(row=0, column=1, sticky="nsew", padx=(10, 0))

        # Create content sections
        self.create_content_options(left_column)
        self.create_voice_options(left_column)
        self.create_subtitle_options(left_column)

        self.create_custom_content(right_column)
        self.create_generation_controls(right_column)
        self.create_status_panel(right_column)

    def create_content_options(self, parent):
        """Create content generation options card"""
        card = ModernCard(parent, title="Content Options")
        card.pack(fill=tk.X, pady=(0, 20))

        # Content grid
        content_grid = tk.Frame(card, bg=ModernColors.SURFACE)
        content_grid.pack(fill=tk.X, pady=10)

        # Configure grid
        content_grid.columnconfigure(1, weight=1)

        # Story Type
        tk.Label(
            content_grid,
            text="Story Type:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=0, column=0, sticky="w", padx=(0, 15), pady=8)

        story_types = [
            "Scary", "Mystery", "Bedtime", "Interesting History", "Urban Legends",
            "Motivational", "Fun Facts", "Long Form Jokes", "Life Pro Tips", "Philosophy",
            "Love", "AITA Stories", "Storytime", "POV", "Day in the Life",
            "True Crime", "Celebrity Facts", "Conspiracy Theories", "Money Saving Tips",
            "Fitness Hacks", "Psychology Facts", "Product Reviews", "Travel Guides",
            "DIY Tutorials", "Cooking Tips", "Dating Advice", "Pet Tips", "Islamic"
        ]

        self.story_combo = ttk.Combobox(
            content_grid,
            textvariable=self.story_type_var,
            values=story_types,
            state="readonly",
            style="Modern.TCombobox"
        )
        self.story_combo.grid(row=0, column=1, sticky="ew", pady=8)

        # Image Style
        tk.Label(
            content_grid,
            text="Image Style:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=1, column=0, sticky="w", padx=(0, 15), pady=8)

        image_styles = [
            "Photorealistic", "Cinematic", "Anime", "Comic Book", "Pixar Art",
            "Digital Art", "Oil Painting", "Watercolor", "Pixel Art", "Dark Aesthetic",
            "Neon Cyberpunk", "Minimalist", "Film Noir", "Retro 80s", "Vaporwave",
            "Cottagecore", "Hyperrealistic", "Flat Design", "3D Cartoon",
            "Pastel Dreamscape", "Fantasy Vibrant", "Nostalgic Filter",
            "VHS Aesthetic", "Y2K", "God Anime Vine", "Ghibli"
        ]

        self.image_style_combo = ttk.Combobox(
            content_grid,
            textvariable=self.image_style_var,
            values=image_styles,
            state="readonly",
            style="Modern.TCombobox"
        )
        self.image_style_combo.grid(row=1, column=1, sticky="ew", pady=8)

        # Image Generator
        tk.Label(
            content_grid,
            text="Image Generator:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=2, column=0, sticky="w", padx=(0, 15), pady=8)

        self.image_model_combo = ttk.Combobox(
            content_grid,
            textvariable=self.image_model_var,
            values=["Replicate Flux", "FAL AI Flux", "Together AI Flux"],
            state="readonly",
            style="Modern.TCombobox"
        )
        self.image_model_combo.grid(row=2, column=1, sticky="ew", pady=8)

        # AI Provider
        tk.Label(
            content_grid,
            text="AI Provider:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=3, column=0, sticky="w", padx=(0, 15), pady=8)

        ai_frame = tk.Frame(content_grid, bg=ModernColors.SURFACE)
        ai_frame.grid(row=3, column=1, sticky="ew", pady=8)

        for i, provider in enumerate(AI_PROVIDERS):
            rb = tk.Radiobutton(
                ai_frame,
                text=provider,
                variable=self.ai_provider_var,
                value=provider,
                bg=ModernColors.SURFACE,
                fg=ModernColors.TEXT_PRIMARY,
                selectcolor=ModernColors.PRIMARY,
                activebackground=ModernColors.SURFACE,
                font=(DEFAULT_FONT, 10),
                command=self.on_ai_provider_change
            )
            rb.pack(side=tk.LEFT, padx=(0, 20))

        # Video Settings
        tk.Label(
            content_grid,
            text="Video Quality:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=4, column=0, sticky="w", padx=(0, 15), pady=8)

        self.video_quality_combo = ttk.Combobox(
            content_grid,
            textvariable=self.video_quality_var,
            values=["720p", "1080p", "2K", "4K"],
            state="readonly",
            style="Modern.TCombobox"
        )
        self.video_quality_combo.grid(row=4, column=1, sticky="ew", pady=8)

        # Orientation
        tk.Label(
            content_grid,
            text="Orientation:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=5, column=0, sticky="w", padx=(0, 15), pady=8)

        orientation_frame = tk.Frame(content_grid, bg=ModernColors.SURFACE)
        orientation_frame.grid(row=5, column=1, sticky="ew", pady=8)

        for orientation, label in [("portrait", "Portrait (9:16)"), ("landscape", "Landscape (16:9)")]:
            rb = tk.Radiobutton(
                orientation_frame,
                text=label,
                variable=self.orientation_var,
                value=orientation,
                bg=ModernColors.SURFACE,
                fg=ModernColors.TEXT_PRIMARY,
                selectcolor=ModernColors.PRIMARY,
                activebackground=ModernColors.SURFACE,
                font=(DEFAULT_FONT, 10)
            )
            rb.pack(side=tk.LEFT, padx=(0, 20))

    def create_voice_options(self, parent):
        """Create voice and audio options card"""
        card = ModernCard(parent, title="Voice & Audio Options")
        card.pack(fill=tk.X, pady=(0, 20))

        # Voice grid
        voice_grid = tk.Frame(card, bg=ModernColors.SURFACE)
        voice_grid.pack(fill=tk.X, pady=10)
        voice_grid.columnconfigure(1, weight=1)

        # TTS Model
        tk.Label(
            voice_grid,
            text="TTS Model:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=0, column=0, sticky="w", padx=(0, 15), pady=8)

        self.tts_combo = ttk.Combobox(
            voice_grid,
            textvariable=self.tts_model_var,
            values=TTS_PROVIDERS,
            state="readonly",
            style="Modern.TCombobox"
        )
        self.tts_combo.grid(row=0, column=1, sticky="ew", pady=8)
        self.tts_combo.bind("<<ComboboxSelected>>", self.on_tts_model_change)

        # Voice Selection
        tk.Label(
            voice_grid,
            text="Voice:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=1, column=0, sticky="w", padx=(0, 15), pady=8)

        voice_frame = tk.Frame(voice_grid, bg=ModernColors.SURFACE)
        voice_frame.grid(row=1, column=1, sticky="ew", pady=8)

        # Load voices based on TTS model
        self.load_voices_for_tts_model()

        self.voice_widget = VoiceSelectionWidget(
            voice_frame,
            values=self.current_voices,
            width=25
        )
        self.voice_widget.pack(fill=tk.X)

        # Preview button
        preview_btn = ModernButton(
            voice_frame,
            text="Preview Voice",
            command=self.preview_voice,
            style="outline",
            width=12
        )
        preview_btn.pack(side=tk.RIGHT, padx=(10, 0))

        # Speech Rate
        tk.Label(
            voice_grid,
            text="Speech Rate:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=2, column=0, sticky="w", padx=(0, 15), pady=8)

        rate_frame = tk.Frame(voice_grid, bg=ModernColors.SURFACE)
        rate_frame.grid(row=2, column=1, sticky="ew", pady=8)

        self.speech_rate_scale = tk.Scale(
            rate_frame,
            from_=0.8,
            to=1.2,
            resolution=0.1,
            orient=tk.HORIZONTAL,
            variable=self.speech_rate_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.speech_rate_scale.pack(fill=tk.X)

        # Background Music
        tk.Label(
            voice_grid,
            text="Background Music:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=3, column=0, sticky="w", padx=(0, 15), pady=8)

        music_options = self.get_music_files()
        self.bg_music_combo = ttk.Combobox(
            voice_grid,
            textvariable=self.bg_music_var,
            values=music_options,
            state="readonly",
            style="Modern.TCombobox"
        )
        self.bg_music_combo.grid(row=3, column=1, sticky="ew", pady=8)

        # Music Volume
        tk.Label(
            voice_grid,
            text="Music Volume:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=4, column=0, sticky="w", padx=(0, 15), pady=8)

        volume_frame = tk.Frame(voice_grid, bg=ModernColors.SURFACE)
        volume_frame.grid(row=4, column=1, sticky="ew", pady=8)

        self.music_volume_scale = tk.Scale(
            volume_frame,
            from_=0.0,
            to=1.0,
            resolution=0.1,
            orient=tk.HORIZONTAL,
            variable=self.bg_music_volume_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.music_volume_scale.pack(fill=tk.X)

    def create_subtitle_options(self, parent):
        """Create subtitle styling options card"""
        card = ModernCard(parent, title="Subtitle Styling")
        card.pack(fill=tk.X, pady=(0, 20))

        # Subtitle grid
        subtitle_grid = tk.Frame(card, bg=ModernColors.SURFACE)
        subtitle_grid.pack(fill=tk.X, pady=10)
        subtitle_grid.columnconfigure(1, weight=1)

        # Font Selection
        tk.Label(
            subtitle_grid,
            text="Font:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=0, column=0, sticky="w", padx=(0, 15), pady=8)

        available_fonts = get_available_fonts()
        if not available_fonts:
            available_fonts = ["TitanOne", "Arial", "Roboto"]

        self.font_combo = ttk.Combobox(
            subtitle_grid,
            textvariable=self.font_var,
            values=available_fonts,
            state="readonly",
            style="Modern.TCombobox"
        )
        self.font_combo.grid(row=0, column=1, sticky="ew", pady=8)

        # Font Color
        tk.Label(
            subtitle_grid,
            text="Font Color:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=1, column=0, sticky="w", padx=(0, 15), pady=8)

        color_frame = tk.Frame(subtitle_grid, bg=ModernColors.SURFACE)
        color_frame.grid(row=1, column=1, sticky="ew", pady=8)

        self.font_color_btn = ModernButton(
            color_frame,
            text="Choose Color",
            command=self.choose_font_color,
            style="outline",
            width=12
        )
        self.font_color_btn.pack(side=tk.LEFT)

        self.font_color_preview = tk.Label(
            color_frame,
            text="  Aa  ",
            bg=self.font_color_var.get(),
            fg="black" if self.font_color_var.get() == "#FFFFFF" else "white",
            font=(DEFAULT_FONT, 12, "bold"),
            relief=tk.SOLID,
            bd=1
        )
        self.font_color_preview.pack(side=tk.LEFT, padx=(10, 0))

        # Font Size
        tk.Label(
            subtitle_grid,
            text="Font Size:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=2, column=0, sticky="w", padx=(0, 15), pady=8)

        size_frame = tk.Frame(subtitle_grid, bg=ModernColors.SURFACE)
        size_frame.grid(row=2, column=1, sticky="ew", pady=8)

        self.font_size_scale = tk.Scale(
            size_frame,
            from_=20,
            to=100,
            orient=tk.HORIZONTAL,
            variable=self.font_size_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.font_size_scale.pack(fill=tk.X)

        # Caption Position
        tk.Label(
            subtitle_grid,
            text="Position:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=3, column=0, sticky="w", padx=(0, 15), pady=8)

        position_frame = tk.Frame(subtitle_grid, bg=ModernColors.SURFACE)
        position_frame.grid(row=3, column=1, sticky="ew", pady=8)

        for position in ["top", "center", "bottom"]:
            rb = tk.Radiobutton(
                position_frame,
                text=position.title(),
                variable=self.caption_position_var,
                value=position,
                bg=ModernColors.SURFACE,
                fg=ModernColors.TEXT_PRIMARY,
                selectcolor=ModernColors.PRIMARY,
                activebackground=ModernColors.SURFACE,
                font=(DEFAULT_FONT, 10)
            )
            rb.pack(side=tk.LEFT, padx=(0, 15))

        # Words per Caption
        tk.Label(
            subtitle_grid,
            text="Words per Caption:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=4, column=0, sticky="w", padx=(0, 15), pady=8)

        words_frame = tk.Frame(subtitle_grid, bg=ModernColors.SURFACE)
        words_frame.grid(row=4, column=1, sticky="ew", pady=8)

        self.caption_words_scale = tk.Scale(
            words_frame,
            from_=1,
            to=10,
            orient=tk.HORIZONTAL,
            variable=self.caption_words_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            highlightthickness=0,
            troughcolor=ModernColors.BG_TERTIARY,
            activebackground=ModernColors.PRIMARY
        )
        self.caption_words_scale.pack(fill=tk.X)

        # Outline Color
        tk.Label(
            subtitle_grid,
            text="Outline Color:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=5, column=0, sticky="w", padx=(0, 15), pady=8)

        outline_color_frame = tk.Frame(subtitle_grid, bg=ModernColors.SURFACE)
        outline_color_frame.grid(row=5, column=1, sticky="ew", pady=8)

        self.outline_color_btn = ModernButton(
            outline_color_frame,
            text="Choose Color",
            command=self.choose_outline_color,
            style="outline",
            width=12
        )
        self.outline_color_btn.pack(side=tk.LEFT)

        self.outline_color_preview = tk.Label(
            outline_color_frame,
            text="  Aa  ",
            bg=self.outline_color_var.get(),
            fg="white" if self.outline_color_var.get() == "#000000" else "black",
            font=(DEFAULT_FONT, 12, "bold"),
            relief=tk.SOLID,
            bd=1
        )
        self.outline_color_preview.pack(side=tk.LEFT, padx=(10, 0))

        # Word Highlighting
        highlight_check = tk.Checkbutton(
            subtitle_grid,
            text="Enable Word Highlighting",
            variable=self.highlight_words_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            selectcolor=ModernColors.PRIMARY,
            activebackground=ModernColors.SURFACE,
            font=(DEFAULT_FONT, 10),
            command=self.toggle_highlight_options
        )
        highlight_check.grid(row=6, column=0, columnspan=2, sticky="w", pady=8)

        # Highlight Color
        tk.Label(
            subtitle_grid,
            text="Highlight Color:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=7, column=0, sticky="w", padx=(0, 15), pady=8)

        highlight_color_frame = tk.Frame(subtitle_grid, bg=ModernColors.SURFACE)
        highlight_color_frame.grid(row=7, column=1, sticky="ew", pady=8)

        self.highlight_color_btn = ModernButton(
            highlight_color_frame,
            text="Choose Color",
            command=self.choose_highlight_color,
            style="outline",
            width=12
        )
        self.highlight_color_btn.pack(side=tk.LEFT)

        self.highlight_color_preview = tk.Label(
            highlight_color_frame,
            text="  Aa  ",
            bg=self.highlight_color_var.get(),
            fg="black" if self.highlight_color_var.get() == "#FFFF00" else "white",
            font=(DEFAULT_FONT, 12, "bold"),
            relief=tk.SOLID,
            bd=1
        )
        self.highlight_color_preview.pack(side=tk.LEFT, padx=(10, 0))

        # Highlight Style
        tk.Label(
            subtitle_grid,
            text="Highlight Style:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11)
        ).grid(row=8, column=0, sticky="w", padx=(0, 15), pady=8)

        highlight_style_frame = tk.Frame(subtitle_grid, bg=ModernColors.SURFACE)
        highlight_style_frame.grid(row=8, column=1, sticky="ew", pady=8)

        for style in ["text_color", "background"]:
            rb = tk.Radiobutton(
                highlight_style_frame,
                text=style.replace("_", " ").title(),
                variable=self.highlight_style_var,
                value=style,
                bg=ModernColors.SURFACE,
                fg=ModernColors.TEXT_PRIMARY,
                selectcolor=ModernColors.PRIMARY,
                activebackground=ModernColors.SURFACE,
                font=(DEFAULT_FONT, 10)
            )
            rb.pack(side=tk.LEFT, padx=(0, 15))

    def create_custom_content(self, parent):
        """Create custom content options card"""
        card = ModernCard(parent, title="Custom Content")
        card.pack(fill=tk.X, pady=(0, 15))

        # Custom title section
        title_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        title_frame.pack(fill=tk.X, pady=(0, 10))

        self.custom_title_check = tk.Checkbutton(
            title_frame,
            text="Use Custom Title",
            variable=self.use_custom_title_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            selectcolor=ModernColors.PRIMARY,
            activebackground=ModernColors.SURFACE,
            font=(DEFAULT_FONT, 10),
            command=self.toggle_custom_title
        )
        self.custom_title_check.pack(anchor="w", pady=(0, 5))

        self.custom_title_entry = tk.Entry(
            title_frame,
            textvariable=self.custom_title_var,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            bd=5,
            state=tk.DISABLED
        )
        self.custom_title_entry.pack(fill=tk.X, pady=(0, 10))

        # Custom script section
        script_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        script_frame.pack(fill=tk.X, pady=(10, 0))

        self.custom_script_check = tk.Checkbutton(
            script_frame,
            text="Use Custom Script",
            variable=self.use_custom_script_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            selectcolor=ModernColors.PRIMARY,
            activebackground=ModernColors.SURFACE,
            font=(DEFAULT_FONT, 10),
            command=self.toggle_custom_script
        )
        self.custom_script_check.pack(anchor="w", pady=(0, 5))

        # Script text area
        script_text_frame = tk.Frame(script_frame, bg=ModernColors.SURFACE)
        script_text_frame.pack(fill=tk.BOTH, expand=True)

        script_scroll = tk.Scrollbar(script_text_frame)
        script_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        self.script_text = tk.Text(
            script_text_frame,
            height=8,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            relief=tk.FLAT,
            bd=5,
            wrap=tk.WORD,
            yscrollcommand=script_scroll.set,
            state=tk.DISABLED
        )
        self.script_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        script_scroll.config(command=self.script_text.yview)

    def create_generation_controls(self, parent):
        """Create generation control buttons"""
        card = ModernCard(parent, title="Generation Controls")
        card.pack(fill=tk.X, pady=(0, 15))

        # Batch generation section
        batch_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        batch_frame.pack(fill=tk.X, pady=(0, 10))

        self.batch_check = tk.Checkbutton(
            batch_frame,
            text="Enable Batch Generation",
            variable=self.batch_enabled_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            selectcolor=ModernColors.PRIMARY,
            activebackground=ModernColors.SURFACE,
            font=(DEFAULT_FONT, 10),
            command=self.toggle_batch_generation
        )
        self.batch_check.pack(anchor="w", pady=(0, 5))

        # Batch quantity
        quantity_frame = tk.Frame(batch_frame, bg=ModernColors.SURFACE)
        quantity_frame.pack(fill=tk.X, pady=(0, 5))

        tk.Label(
            quantity_frame,
            text="Number of Videos:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10)
        ).pack(side=tk.LEFT, padx=(0, 10))

        self.batch_quantity_spinbox = tk.Spinbox(
            quantity_frame,
            from_=1,
            to=20,
            textvariable=self.batch_quantity_var,
            width=5,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 10),
            state="disabled"
        )
        self.batch_quantity_spinbox.pack(side=tk.LEFT)

        # Custom titles from file
        titles_frame = tk.Frame(batch_frame, bg=ModernColors.SURFACE)
        titles_frame.pack(fill=tk.X, pady=(5, 0))

        self.batch_custom_titles_check = tk.Checkbutton(
            titles_frame,
            text="Use Custom Titles from File",
            variable=self.batch_custom_titles_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            selectcolor=ModernColors.PRIMARY,
            activebackground=ModernColors.SURFACE,
            font=(DEFAULT_FONT, 10),
            command=self.toggle_batch_custom_titles,
            state="disabled"
        )
        self.batch_custom_titles_check.pack(anchor="w", pady=(0, 5))

        # File selection
        file_frame = tk.Frame(titles_frame, bg=ModernColors.SURFACE)
        file_frame.pack(fill=tk.X)

        self.titles_file_entry = tk.Entry(
            file_frame,
            textvariable=self.titles_file_var,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 9),
            relief=tk.FLAT,
            bd=5,
            state="disabled"
        )
        self.titles_file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        self.browse_titles_btn = ModernButton(
            file_frame,
            text="Browse",
            command=self.browse_titles_file,
            style="outline",
            width=8,
            state=tk.DISABLED
        )
        self.browse_titles_btn.pack(side=tk.RIGHT)

        # Main generate button
        self.generate_btn = ModernButton(
            card,
            text="🎬 GENERATE VIDEO",
            command=self.generate_video,
            style="primary",
            width=20
        )
        self.generate_btn.pack(pady=(10, 10))

        # Control buttons frame
        controls_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        controls_frame.pack(fill=tk.X)

        self.pause_btn = ModernButton(
            controls_frame,
            text="⏸️ Pause",
            command=self.pause_generation,
            style="outline",
            width=8,
            state=tk.DISABLED
        )
        self.pause_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_btn = ModernButton(
            controls_frame,
            text="⏹️ Stop",
            command=self.stop_generation,
            style="outline",
            width=8,
            state=tk.DISABLED
        )
        self.stop_btn.pack(side=tk.LEFT, padx=(5, 0))

        # Quick actions
        actions_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        actions_frame.pack(fill=tk.X, pady=(10, 0))

        refresh_btn = ModernButton(
            actions_frame,
            text="🔄 Refresh",
            command=self.refresh_resources,
            style="ghost",
            width=10
        )
        refresh_btn.pack(side=tk.LEFT, padx=(0, 5))

        settings_btn = ModernButton(
            actions_frame,
            text="⚙️ Settings",
            command=self.show_settings,
            style="ghost",
            width=10
        )
        settings_btn.pack(side=tk.LEFT, padx=(5, 0))

    def create_status_panel(self, parent):
        """Create status and progress panel"""
        card = ModernCard(parent, title="Generation Status")
        card.pack(fill=tk.BOTH, expand=True)

        # Progress section
        progress_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        progress_frame.pack(fill=tk.X, pady=(0, 15))

        tk.Label(
            progress_frame,
            text="Progress:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11, "bold")
        ).pack(anchor="w", pady=(0, 5))

        self.progress_bar = ttk.Progressbar(
            progress_frame,
            variable=self.progress_var,
            maximum=100,
            length=300,
            mode='determinate'
        )
        self.progress_bar.pack(fill=tk.X, pady=(0, 10))

        # Status message
        self.status_label = tk.Label(
            progress_frame,
            textvariable=self.status_var,
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 10),
            wraplength=300,
            justify=tk.LEFT
        )
        self.status_label.pack(anchor="w")

        # Console log
        log_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        log_frame.pack(fill=tk.BOTH, expand=True, pady=(15, 0))

        tk.Label(
            log_frame,
            text="Console Log:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11, "bold")
        ).pack(anchor="w", pady=(0, 5))

        # Log text area
        log_text_frame = tk.Frame(log_frame, bg=ModernColors.SURFACE)
        log_text_frame.pack(fill=tk.BOTH, expand=True)

        log_scroll = tk.Scrollbar(log_text_frame)
        log_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        self.log_text = tk.Text(
            log_text_frame,
            height=12,
            bg=ModernColors.BG_TERTIARY,
            fg=ModernColors.TEXT_SECONDARY,
            font=("Consolas", 9),
            relief=tk.FLAT,
            bd=5,
            wrap=tk.WORD,
            yscrollcommand=log_scroll.set,
            state=tk.DISABLED
        )
        self.log_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        log_scroll.config(command=self.log_text.yview)

        # Recent video section
        recent_frame = tk.Frame(card, bg=ModernColors.SURFACE)
        recent_frame.pack(fill=tk.X, pady=(15, 0))

        tk.Label(
            recent_frame,
            text="Recent Video:",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_PRIMARY,
            font=(DEFAULT_FONT, 11, "bold")
        ).pack(anchor="w", pady=(0, 5))

        self.recent_video_label = tk.Label(
            recent_frame,
            text="No videos generated yet",
            bg=ModernColors.SURFACE,
            fg=ModernColors.TEXT_SECONDARY,
            font=(DEFAULT_FONT, 9),
            wraplength=300,
            justify=tk.LEFT
        )
        self.recent_video_label.pack(anchor="w", pady=(0, 10))

        # Video action buttons
        video_actions = tk.Frame(recent_frame, bg=ModernColors.SURFACE)
        video_actions.pack(fill=tk.X)

        self.open_video_btn = ModernButton(
            video_actions,
            text="📹 Open Video",
            command=self.open_recent_video,
            style="secondary",
            width=12,
            state=tk.DISABLED
        )
        self.open_video_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.open_folder_btn = ModernButton(
            video_actions,
            text="📁 Open Folder",
            command=self.open_video_folder,
            style="outline",
            width=12,
            state=tk.DISABLED
        )
        self.open_folder_btn.pack(side=tk.LEFT, padx=(5, 0))

        # Initialize log
        self.log_message("Modern UI initialized - Ready to generate videos")

    # Supporting methods
    def load_voices_for_tts_model(self):
        """Load voices based on selected TTS model"""
        tts_model = self.tts_model_var.get()

        if tts_model == "OpenAI":
            self.current_voices = ["alloy", "echo", "fable", "onyx", "nova", "shimmer"]
        elif tts_model == "Voicely":
            # Load from JSON file like in main.py
            try:
                with open(os.path.join(script_dir, "english_edge_voices.json"), "r") as f:
                    english_voices = json.load(f)
                self.current_voices = [voice["ShortName"] for voice in english_voices]
            except:
                self.current_voices = ["en-US-AriaNeural", "en-US-JennyNeural", "en-US-GuyNeural"]
        elif tts_model == "ElevenLabs":
            # Load ElevenLabs voices from API
            self.load_elevenlabs_voices()
        else:
            self.current_voices = ["alloy"]

    def load_elevenlabs_voices(self):
        """Load ElevenLabs voices from API with comprehensive error handling"""
        # Load voices asynchronously to avoid blocking UI
        threading.Thread(target=self._load_elevenlabs_voices_async, daemon=True).start()

    def load_elevenlabs_voices_sync(self):
        """Load ElevenLabs voices synchronously (for testing)"""
        try:
            # Import ElevenLabs client
            from elevenlabs_client import elevenlabs_client

            # Check if API key is available
            if not elevenlabs_client.api_key:
                self.current_voices = ["ElevenLabs API key not set"]
                self.log_message("ElevenLabs API key not found. Please set your API key in settings.")
                return

            # Check if ElevenLabs client is available
            if not elevenlabs_client.is_available:
                self.current_voices = ["ElevenLabs service unavailable"]
                self.log_message("ElevenLabs service is not available. Please check your API key.")
                return

            # Fetch voices from API
            voices = elevenlabs_client.get_voices()

            if voices:
                # Create voice options with name and ID mapping
                self.elevenlabs_voice_mapping = {}
                voice_options = []

                for voice in voices:
                    voice_name = voice.get('name', 'Unknown Voice')
                    voice_id = voice.get('voice_id', '')

                    # Store mapping for later use
                    self.elevenlabs_voice_mapping[voice_name] = voice_id
                    voice_options.append(voice_name)

                self.current_voices = voice_options
                self.log_message(f"Successfully loaded {len(voices)} ElevenLabs voices")

                # Update voice widget if it exists
                if hasattr(self, 'voice_widget'):
                    self.voice_widget.configure(values=self.current_voices)
                    if self.current_voices:
                        self.voice_widget.set(self.current_voices[0])

            else:
                # No voices found
                self.current_voices = ["No ElevenLabs voices found"]
                self.log_message("No ElevenLabs voices found. Please check your API key and account.")

        except ImportError:
            # ElevenLabs client not available
            self.current_voices = ["ElevenLabs client not installed"]
            self.log_message("ElevenLabs client not installed. Please install the elevenlabs package.")

        except Exception as e:
            # General error handling
            error_message = f"Error loading ElevenLabs voices: {str(e)}"
            self.current_voices = ["Error loading ElevenLabs voices"]
            self.log_message(error_message)

    def _load_elevenlabs_voices_async(self):
        """Asynchronously load ElevenLabs voices from API"""
        try:
            # Import ElevenLabs client
            from elevenlabs_client import elevenlabs_client

            # Show loading state on main thread (if main loop is running)
            try:
                self.root.after(0, self._update_voices_loading_state)
            except RuntimeError:
                # Main loop not running, update directly
                self._update_voices_loading_state()

            # Check if API key is available
            if not elevenlabs_client.api_key:
                try:
                    self.root.after(0, lambda: self._update_voices_error_state(
                        "ElevenLabs API key not set",
                        "ElevenLabs API key not found. Please set your API key in settings.",
                        "ElevenLabs API Key",
                        "ElevenLabs API key not set. Please add your API key in the Settings tab."
                    ))
                except RuntimeError:
                    # Main loop not running, update directly
                    self._update_voices_error_state(
                        "ElevenLabs API key not set",
                        "ElevenLabs API key not found. Please set your API key in settings.",
                        "ElevenLabs API Key",
                        "ElevenLabs API key not set. Please add your API key in the Settings tab."
                    )
                return

            # Check if ElevenLabs client is available
            if not elevenlabs_client.is_available:
                try:
                    self.root.after(0, lambda: self._update_voices_error_state(
                        "ElevenLabs service unavailable",
                        "ElevenLabs service is not available. Please check your API key.",
                        "ElevenLabs Unavailable",
                        "ElevenLabs service is not available. Please check your API key and internet connection.",
                        is_error=True
                    ))
                except RuntimeError:
                    # Main loop not running, update directly
                    self._update_voices_error_state(
                        "ElevenLabs service unavailable",
                        "ElevenLabs service is not available. Please check your API key.",
                        "ElevenLabs Unavailable",
                        "ElevenLabs service is not available. Please check your API key and internet connection.",
                        is_error=True
                    )
                return

            # Fetch voices from API
            voices = elevenlabs_client.get_voices()

            if voices:
                # Create voice options with name and ID mapping
                voice_mapping = {}
                voice_options = []

                for voice in voices:
                    voice_name = voice.get('name', 'Unknown Voice')
                    voice_id = voice.get('voice_id', '')

                    # Store mapping for later use
                    voice_mapping[voice_name] = voice_id
                    voice_options.append(voice_name)

                # Update UI on main thread (if main loop is running)
                try:
                    self.root.after(0, lambda: self._update_voices_success_state(
                        voice_options, voice_mapping, len(voices)
                    ))
                except RuntimeError:
                    # Main loop not running, update directly
                    self._update_voices_success_state(voice_options, voice_mapping, len(voices))

            else:
                # No voices found
                try:
                    self.root.after(0, lambda: self._update_voices_error_state(
                        "No ElevenLabs voices found",
                        "No ElevenLabs voices found. Please check your API key and account.",
                        "No Voices Found",
                        "No ElevenLabs voices found. Please check your API key and account status."
                    ))
                except RuntimeError:
                    # Main loop not running, update directly
                    self._update_voices_error_state(
                        "No ElevenLabs voices found",
                        "No ElevenLabs voices found. Please check your API key and account.",
                        "No Voices Found",
                        "No ElevenLabs voices found. Please check your API key and account status."
                    )

        except ImportError:
            # ElevenLabs client not available
            try:
                self.root.after(0, lambda: self._update_voices_error_state(
                    "ElevenLabs client not installed",
                    "ElevenLabs client not installed. Please install the elevenlabs package.",
                    "ElevenLabs Not Installed",
                    "ElevenLabs client not installed. Please install with: pip install elevenlabs",
                    is_error=True
                ))
            except RuntimeError:
                # Main loop not running, update directly
                self._update_voices_error_state(
                    "ElevenLabs client not installed",
                    "ElevenLabs client not installed. Please install the elevenlabs package.",
                    "ElevenLabs Not Installed",
                    "ElevenLabs client not installed. Please install with: pip install elevenlabs",
                    is_error=True
                )

        except Exception as e:
            # General error handling
            error_message = f"Error loading ElevenLabs voices: {str(e)}"
            try:
                self.root.after(0, lambda: self._update_voices_error_state(
                    "Error loading ElevenLabs voices",
                    error_message,
                    "ElevenLabs Error",
                    error_message,
                    is_error=True
                ))
            except RuntimeError:
                # Main loop not running, update directly
                self._update_voices_error_state(
                    "Error loading ElevenLabs voices",
                    error_message,
                    "ElevenLabs Error",
                    error_message,
                    is_error=True
                )

    def _update_voices_loading_state(self):
        """Update UI to show loading state"""
        self.current_voices = ["Loading ElevenLabs voices..."]
        if hasattr(self, 'voice_widget'):
            self.voice_widget.configure(values=self.current_voices)
            self.voice_widget.set(self.current_voices[0])
        self.log_message("Loading ElevenLabs voices...")

    def _update_voices_error_state(self, voice_text, log_message, dialog_title, dialog_message, is_error=False):
        """Update UI to show error state"""
        self.current_voices = [voice_text]
        if hasattr(self, 'voice_widget'):
            self.voice_widget.configure(values=self.current_voices)
            self.voice_widget.set(self.current_voices[0])
        self.log_message(log_message)

        # Only show dialog if we're in a proper UI context
        try:
            if is_error:
                messagebox.showerror(dialog_title, dialog_message)
            else:
                messagebox.showwarning(dialog_title, dialog_message)
        except Exception:
            # Can't show dialog (e.g., in testing), just log the message
            print(f"Dialog would show: {dialog_title}: {dialog_message}")

    def _update_voices_success_state(self, voice_options, voice_mapping, voice_count):
        """Update UI to show successful voice loading"""
        self.current_voices = voice_options
        self.elevenlabs_voice_mapping = voice_mapping

        if hasattr(self, 'voice_widget'):
            self.voice_widget.configure(values=self.current_voices)
            if self.current_voices:
                self.voice_widget.set(self.current_voices[0])

        self.log_message(f"Successfully loaded {voice_count} ElevenLabs voices")

    def refresh_elevenlabs_voices(self):
        """Manually refresh ElevenLabs voices"""
        if self.tts_model_var.get() == "ElevenLabs":
            self.log_message("Refreshing ElevenLabs voices...")
            self.load_elevenlabs_voices()
        else:
            messagebox.showinfo(
                "Refresh Voices",
                "Please select ElevenLabs as your TTS model first, then try refreshing voices."
            )

    def get_music_files(self):
        """Get available background music files"""
        music_options = ["None"]
        music_dir = os.path.join(os.path.dirname(script_dir), "music")

        if os.path.exists(music_dir):
            for file in os.listdir(music_dir):
                if file.lower().endswith(".mp3"):
                    music_name = os.path.splitext(file)[0]
                    music_options.append(music_name)

        return music_options

    def get_bg_music_path(self, music_name):
        """Get the full path for background music file"""
        if music_name == "None" or not music_name:
            return None

        music_dir = os.path.join(os.path.dirname(script_dir), "music")
        music_path = os.path.join(music_dir, f"{music_name}.mp3")

        if os.path.exists(music_path):
            return music_path
        return None

    def log_message(self, message):
        """Add a message to the log console"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.log_messages.append(log_entry)

        # Update log display
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, log_entry + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)
        self.root.update()

    def update_status(self, message):
        """Update the status message"""
        self.status_var.set(message)
        self.log_message(message)

    def update_progress(self, value):
        """Update the progress bar"""
        self.progress_var.set(value)
        self.root.update()

    # Event handlers
    def on_ai_provider_change(self):
        """Handle AI provider change"""
        provider = self.ai_provider_var.get()
        success, message = switch_ai_provider(provider)
        if success:
            self.log_message(f"Switched to {provider} AI provider")
        else:
            self.log_message(f"Failed to switch to {provider}: {message}")

    def on_tts_model_change(self, event=None):
        """Handle TTS model change"""
        self.load_voices_for_tts_model()
        if hasattr(self, 'voice_widget'):
            self.voice_widget.configure(values=self.current_voices)
            if self.current_voices:
                self.voice_widget.set(self.current_voices[0])
        self.log_message(f"Changed TTS model to {self.tts_model_var.get()}")

    def preview_voice(self):
        """Preview the selected voice"""
        voice = self.voice_widget.get() if hasattr(self, 'voice_widget') else self.voice_var.get()
        tts_model = self.tts_model_var.get()

        if voice:
            self.log_message(f"Previewing voice: {voice}")
            # Use the voice preview functionality from main.py
            threading.Thread(
                target=lambda: voice_preview.generate_and_play_preview(
                    voice, tts_model, lambda: self.log_message("Voice preview completed")
                ),
                daemon=True
            ).start()

    def choose_font_color(self):
        """Open color chooser for font color"""
        color = colorchooser.askcolor(initialcolor=self.font_color_var.get())
        if color[1]:
            self.font_color_var.set(color[1])
            self.font_color_preview.config(
                bg=color[1],
                fg="black" if color[1] == "#FFFFFF" else "white"
            )

    def choose_outline_color(self):
        """Open color chooser for outline color"""
        color = colorchooser.askcolor(initialcolor=self.outline_color_var.get())
        if color[1]:
            self.outline_color_var.set(color[1])
            self.outline_color_preview.config(
                bg=color[1],
                fg="white" if color[1] == "#000000" else "black"
            )

    def choose_highlight_color(self):
        """Open color chooser for highlight color"""
        color = colorchooser.askcolor(initialcolor=self.highlight_color_var.get())
        if color[1]:
            self.highlight_color_var.set(color[1])
            self.highlight_color_preview.config(
                bg=color[1],
                fg="black" if color[1] == "#FFFF00" else "white"
            )

    def toggle_highlight_options(self):
        """Toggle highlight options based on checkbox state"""
        # This method can be used to enable/disable highlight-related controls
        # Currently just a placeholder for future enhancements
        pass

    def toggle_batch_generation(self):
        """Toggle batch generation controls"""
        if self.batch_enabled_var.get():
            # Enable batch generation fields
            self.batch_quantity_spinbox.config(state="normal")
            self.batch_custom_titles_check.config(state="normal")
            self.toggle_batch_custom_titles()
        else:
            # Disable batch generation fields
            self.batch_quantity_spinbox.config(state="disabled")
            self.batch_custom_titles_check.config(state="disabled")
            self.titles_file_entry.config(state="disabled")
            self.browse_titles_btn.config(state=tk.DISABLED)

    def toggle_batch_custom_titles(self):
        """Toggle custom titles from file controls"""
        if self.batch_enabled_var.get() and self.batch_custom_titles_var.get():
            self.titles_file_entry.config(state="normal")
            self.browse_titles_btn.config(state=tk.NORMAL)
        else:
            self.titles_file_entry.config(state="disabled")
            self.browse_titles_btn.config(state=tk.DISABLED)

    def browse_titles_file(self):
        """Browse for titles file"""
        file_path = filedialog.askopenfilename(
            title="Select Titles File",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if file_path:
            self.titles_file_var.set(file_path)
            # Validate the file
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    titles = [line.strip() for line in f.readlines() if line.strip()]

                batch_quantity = int(self.batch_quantity_var.get())

                if len(titles) < batch_quantity:
                    messagebox.showwarning(
                        "Title Count Mismatch",
                        f"The file contains {len(titles)} titles, but you've set {batch_quantity} videos to generate.\n\n"
                        f"Either add more titles to the file or reduce the batch quantity."
                    )
                elif len(titles) > batch_quantity:
                    messagebox.showinfo(
                        "Extra Titles",
                        f"The file contains {len(titles)} titles, but you've set {batch_quantity} videos to generate.\n\n"
                        f"Only the first {batch_quantity} titles will be used."
                    )
            except Exception as e:
                messagebox.showerror("Error Reading File", f"Could not read the titles file: {str(e)}")

    def logout(self):
        """Log out the current user and restart the application"""
        from auth import logout_user

        # Confirm logout
        if messagebox.askyesno("Confirm Logout", "Are you sure you want to log out?"):
            # Clear credentials
            if logout_user():
                messagebox.showinfo("Logout Successful", "You have been logged out successfully.")

                # Restart the application
                self.root.destroy()

                # Re-launch the application
                import sys
                import subprocess

                # Get the current script path
                script_path = sys.argv[0]

                # Start a new process with the same arguments
                subprocess.Popen([sys.executable, script_path] + sys.argv[1:])
            else:
                messagebox.showerror("Logout Failed", "Failed to log out. Please try again.")

    def toggle_custom_title(self):
        """Toggle custom title entry"""
        if self.use_custom_title_var.get():
            self.custom_title_entry.config(state=tk.NORMAL)
        else:
            self.custom_title_entry.config(state=tk.DISABLED)
            self.custom_title_var.set("")

    def toggle_custom_script(self):
        """Toggle custom script text area"""
        if self.use_custom_script_var.get():
            self.script_text.config(state=tk.NORMAL)
        else:
            self.script_text.config(state=tk.DISABLED)
            self.script_text.delete(1.0, tk.END)

    # Main functionality methods
    def generate_video(self):
        """Start video generation process"""
        if self.is_processing:
            messagebox.showwarning("Warning", "Video generation is already in progress!")
            return

        # Validate inputs
        if not self.validate_inputs():
            return

        # Check if batch generation is enabled
        is_batch = self.batch_enabled_var.get()

        if is_batch:
            try:
                quantity = int(self.batch_quantity_var.get())
                if quantity < 1 or quantity > 50:
                    messagebox.showerror("Error", "Batch quantity must be between 1 and 50")
                    return

                # Initialize batch variables
                self.batch_current = 1
                self.batch_total = quantity

                # Check if custom titles from file is enabled
                self.batch_titles = None
                if self.batch_custom_titles_var.get():
                    titles_file = self.titles_file_var.get()
                    if not titles_file or not os.path.exists(titles_file):
                        messagebox.showerror("Error", "Please select a valid titles file")
                        return

                    try:
                        with open(titles_file, 'r', encoding='utf-8') as f:
                            self.batch_titles = [line.strip() for line in f.readlines() if line.strip()]

                        if len(self.batch_titles) < quantity:
                            messagebox.showerror("Error", f"Titles file contains only {len(self.batch_titles)} titles, but {quantity} videos requested")
                            return
                    except Exception as e:
                        messagebox.showerror("Error", f"Could not read titles file: {str(e)}")
                        return

                self.update_status(f"Starting batch generation: video 1 of {quantity}")
            except ValueError:
                messagebox.showerror("Error", "Invalid batch quantity")
                return
        else:
            self.update_status("Generating video...")
            self.batch_titles = None

        # Prepare generation parameters
        params = self.prepare_generation_params()

        # Update UI state
        self.is_processing = True
        self.generate_btn.config(state=tk.DISABLED, text="Generating...")
        self.pause_btn.config(state=tk.NORMAL)
        self.stop_btn.config(state=tk.NORMAL)

        # Handle custom titles from batch file if enabled for the first video
        ai_custom_title = params['custom_title']
        if self.batch_titles and is_batch and self.batch_current <= len(self.batch_titles):
            # Get the title for the current batch index (first video)
            batch_title = self.batch_titles[self.batch_current - 1]

            if params['custom_script']:
                # Use as custom title for custom script
                params['custom_title'] = batch_title
                self.update_status(f"Using custom title from file: {batch_title}")
            else:
                # Use as AI custom title
                ai_custom_title = batch_title
                self.update_status(f"Using AI title from file: {batch_title}")

        # Start generation thread
        self.generator_thread = VideoGeneratorThread(
            story_type=params['story_type'],
            image_style=params['image_style'],
            tts_model=params['tts_model'],
            image_model=params['image_model'],
            voice_name=params['voice'],
            font_name=params['font'],
            font_color=params['font_color'],
            font_size=params['font_size'],
            outline_color=params['outline_color'],
            outline_size=params['outline_size'],
            caption_position=params['caption_position'],
            caption_words=params['caption_words'],
            callback=self.update_status,
            update_progress=self.update_progress,
            highlight_words=params['highlight_words'],
            highlight_color=params['highlight_color'],
            custom_script=params['custom_script'],
            custom_title=params['custom_title'],
            bg_music_path=self.get_bg_music_path(params['bg_music']),
            bg_music_volume=params['bg_music_volume'],
            llm_provider=params['ai_provider'],
            ai_custom_title=ai_custom_title,
            end_pause_duration=params['end_pause'],
            orientation=params['orientation'],
            video_quality=params['video_quality'],
            highlight_style=params['highlight_style'],
            highlight_bg_color=params['highlight_bg_color'],
            highlight_bg_opacity=params['highlight_bg_opacity']
        )
        self.generator_thread.start()

        self.log_message("Starting video generation...")

        # Start monitoring thread completion
        self.monitor_generation_completion()

    def monitor_generation_completion(self):
        """Monitor the generation thread for completion"""
        if self.generator_thread and self.generator_thread.is_alive():
            # Check again in 1 second
            self.root.after(1000, self.monitor_generation_completion)
        else:
            # Thread has completed, handle the result
            if self.generator_thread and hasattr(self.generator_thread, 'result'):
                result = self.generator_thread.result
                if result:
                    success = result.get('success', False)
                    video_path = result.get('video_path')
                    error_message = result.get('message')

                    # Handle batch generation
                    if self.batch_enabled_var.get() and success:
                        # Check if we have more videos to generate
                        if self.batch_current < self.batch_total:
                            self.batch_current += 1
                            self.progress_var.set(0)
                            self.update_status(f"Starting batch generation: video {self.batch_current} of {self.batch_total}")

                            # Prepare parameters for next video
                            params = self.prepare_generation_params()

                            # Handle custom titles from batch file if enabled
                            ai_custom_title = params['custom_title']
                            if self.batch_titles and self.batch_current <= len(self.batch_titles):
                                batch_title = self.batch_titles[self.batch_current - 1]

                                if params['custom_script']:
                                    params['custom_title'] = batch_title
                                    self.update_status(f"Using custom title from file: {batch_title}")
                                else:
                                    ai_custom_title = batch_title
                                    self.update_status(f"Using AI title from file: {batch_title}")

                            # Start next generation
                            self.generator_thread = VideoGeneratorThread(
                                story_type=params['story_type'],
                                image_style=params['image_style'],
                                tts_model=params['tts_model'],
                                image_model=params['image_model'],
                                voice_name=params['voice'],
                                font_name=params['font'],
                                font_color=params['font_color'],
                                font_size=params['font_size'],
                                outline_color=params['outline_color'],
                                outline_size=params['outline_size'],
                                caption_position=params['caption_position'],
                                caption_words=params['caption_words'],
                                callback=self.update_status,
                                update_progress=self.update_progress,
                                highlight_words=params['highlight_words'],
                                highlight_color=params['highlight_color'],
                                custom_script=params['custom_script'],
                                custom_title=params['custom_title'],
                                bg_music_path=self.get_bg_music_path(params['bg_music']),
                                bg_music_volume=params['bg_music_volume'],
                                llm_provider=params['ai_provider'],
                                ai_custom_title=ai_custom_title,
                                end_pause_duration=params['end_pause'],
                                orientation=params['orientation'],
                                video_quality=params['video_quality'],
                                highlight_style=params['highlight_style'],
                                highlight_bg_color=params['highlight_bg_color'],
                                highlight_bg_opacity=params['highlight_bg_opacity']
                            )
                            self.generator_thread.start()

                            # Continue monitoring
                            self.root.after(1000, self.monitor_generation_completion)
                            return
                        else:
                            # All videos in batch completed
                            self.update_status(f"Batch generation completed! Generated {self.batch_total} videos.")

                    self.on_generation_complete(success, video_path, error_message)
                else:
                    self.on_generation_complete(False, None, "Generation completed without result")

    def validate_inputs(self):
        """Validate user inputs before generation"""
        # Check custom script if enabled
        if self.use_custom_script_var.get():
            script_content = self.script_text.get(1.0, tk.END).strip()
            if not script_content:
                messagebox.showerror("Error", "Custom script cannot be empty!")
                return False

        # Check custom title if enabled
        if self.use_custom_title_var.get():
            title_content = self.custom_title_var.get().strip()
            if not title_content:
                messagebox.showerror("Error", "Custom title cannot be empty!")
                return False

        # Check voice selection
        voice = self.voice_widget.get() if hasattr(self, 'voice_widget') else self.voice_var.get()
        if not voice:
            messagebox.showerror("Error", "Please select a voice!")
            return False

        # Check for ElevenLabs error states
        tts_model = self.tts_model_var.get()
        if tts_model == "ElevenLabs":
            error_states = [
                "Loading ElevenLabs voices...",
                "ElevenLabs API key not set",
                "ElevenLabs service unavailable",
                "No ElevenLabs voices found",
                "ElevenLabs client not installed",
                "Error loading ElevenLabs voices"
            ]

            if voice in error_states:
                messagebox.showerror(
                    "ElevenLabs Error",
                    f"Cannot generate video: {voice}\n\nPlease resolve the ElevenLabs issue before proceeding."
                )
                return False

            # Check if voice mapping exists for ElevenLabs
            if hasattr(self, 'elevenlabs_voice_mapping') and voice not in self.elevenlabs_voice_mapping:
                messagebox.showerror(
                    "Voice Error",
                    f"Selected voice '{voice}' is not available. Please select a different voice."
                )
                return False

        return True

    def prepare_generation_params(self):
        """Prepare parameters for video generation"""
        # Get voice selection
        voice = self.voice_widget.get() if hasattr(self, 'voice_widget') else self.voice_var.get()

        # Handle ElevenLabs voice ID mapping
        tts_model = self.tts_model_var.get()
        if tts_model == "ElevenLabs" and hasattr(self, 'elevenlabs_voice_mapping'):
            # Convert voice name to voice ID for ElevenLabs
            if voice in self.elevenlabs_voice_mapping:
                voice = self.elevenlabs_voice_mapping[voice]
                self.log_message(f"Using ElevenLabs voice ID: {voice}")
            else:
                self.log_message(f"Warning: ElevenLabs voice '{voice}' not found in mapping")

        # Get custom content
        custom_script = None
        custom_title = None

        if self.use_custom_script_var.get():
            custom_script = self.script_text.get(1.0, tk.END).strip()

        if self.use_custom_title_var.get():
            custom_title = self.custom_title_var.get().strip()

        # Prepare parameters dictionary
        params = {
            'story_type': self.story_type_var.get(),
            'image_style': self.image_style_var.get(),
            'image_model': self.image_model_var.get(),
            'ai_provider': self.ai_provider_var.get(),
            'tts_model': tts_model,
            'voice': voice,
            'speech_rate': self.speech_rate_var.get(),
            'video_quality': self.video_quality_var.get(),
            'orientation': self.orientation_var.get(),
            'bg_music': self.bg_music_var.get(),
            'bg_music_volume': self.bg_music_volume_var.get(),
            'end_pause': self.end_pause_var.get(),
            'font': self.font_var.get(),
            'font_color': self.font_color_var.get(),
            'font_size': self.font_size_var.get(),
            'outline_color': self.outline_color_var.get(),
            'outline_size': self.outline_size_var.get(),
            'caption_position': self.caption_position_var.get(),
            'caption_words': self.caption_words_var.get(),
            'highlight_words': self.highlight_words_var.get(),
            'highlight_color': self.highlight_color_var.get(),
            'highlight_style': self.highlight_style_var.get(),
            'highlight_bg_color': self.highlight_bg_color_var.get(),
            'highlight_bg_opacity': self.highlight_bg_opacity_var.get(),
            'custom_script': custom_script,
            'custom_title': custom_title
        }

        return params

    def pause_generation(self):
        """Pause video generation"""
        if self.generator_thread and self.generator_thread.is_alive():
            self.generator_thread.pause()
            self.pause_btn.config(text="▶️ Resume")
            self.pause_btn.config(command=self.resume_generation)
            self.log_message("Generation paused")

    def resume_generation(self):
        """Resume video generation"""
        if self.generator_thread and self.generator_thread.is_alive():
            self.generator_thread.resume()
            self.pause_btn.config(text="⏸️ Pause")
            self.pause_btn.config(command=self.pause_generation)
            self.log_message("Generation resumed")

    def stop_generation(self):
        """Stop video generation"""
        if self.generator_thread and self.generator_thread.is_alive():
            self.generator_thread.stop()
            self.log_message("Generation stopped by user")
        self.reset_ui_state()

    def on_generation_complete(self, success, video_path=None, error_message=None):
        """Handle generation completion with enhanced ElevenLabs error handling"""
        self.reset_ui_state()

        if success and video_path:
            self.most_recent_video = video_path
            self.recent_video_label.config(text=f"✅ {os.path.basename(video_path)}")
            self.open_video_btn.config(state=tk.NORMAL)
            self.open_folder_btn.config(state=tk.NORMAL)
            self.log_message(f"Video generation completed: {video_path}")

            # Show completion notification
            messagebox.showinfo("Success", f"Video generated successfully!\n\nSaved to: {video_path}")
        else:
            error_msg = error_message or "Unknown error occurred"
            self.log_message(f"Generation failed: {error_msg}")

            # Enhanced error handling for ElevenLabs-specific issues
            if self._is_elevenlabs_error(error_msg):
                self._handle_elevenlabs_error(error_msg)
            else:
                messagebox.showerror("Error", f"Video generation failed:\n\n{error_msg}")

    def _is_elevenlabs_error(self, error_message):
        """Check if the error is related to ElevenLabs"""
        elevenlabs_keywords = [
            "elevenlabs", "eleven labs", "voice_id", "tts", "text-to-speech",
            "api key", "voice not found", "audio generation", "speech synthesis"
        ]

        error_lower = error_message.lower()
        return any(keyword in error_lower for keyword in elevenlabs_keywords)

    def _handle_elevenlabs_error(self, error_message):
        """Handle ElevenLabs-specific errors with helpful guidance"""
        error_lower = error_message.lower()

        if "api key" in error_lower or "unauthorized" in error_lower:
            # API key issue
            messagebox.showerror(
                "ElevenLabs API Key Error",
                f"ElevenLabs API key issue:\n\n{error_message}\n\n"
                "Please check your ElevenLabs API key in Settings and ensure it's valid."
            )
        elif "voice" in error_lower and ("not found" in error_lower or "invalid" in error_lower):
            # Voice not found issue
            messagebox.showerror(
                "ElevenLabs Voice Error",
                f"ElevenLabs voice issue:\n\n{error_message}\n\n"
                "The selected voice may no longer be available. Please try:\n"
                "1. Refreshing voices by switching TTS models\n"
                "2. Selecting a different voice\n"
                "3. Checking your ElevenLabs account"
            )
        elif "quota" in error_lower or "limit" in error_lower or "usage" in error_lower:
            # Quota/limit issue
            messagebox.showerror(
                "ElevenLabs Quota Error",
                f"ElevenLabs usage limit reached:\n\n{error_message}\n\n"
                "Please check your ElevenLabs account usage and upgrade if needed."
            )
        elif "network" in error_lower or "connection" in error_lower or "timeout" in error_lower:
            # Network issue
            messagebox.showerror(
                "ElevenLabs Connection Error",
                f"ElevenLabs connection issue:\n\n{error_message}\n\n"
                "Please check your internet connection and try again."
            )
        else:
            # General ElevenLabs error
            messagebox.showerror(
                "ElevenLabs Error",
                f"ElevenLabs service error:\n\n{error_message}\n\n"
                "Please try again or switch to a different TTS provider."
            )

    def reset_ui_state(self):
        """Reset UI to ready state"""
        self.is_processing = False
        self.generate_btn.config(state=tk.NORMAL, text="🎬 GENERATE VIDEO")
        self.pause_btn.config(state=tk.DISABLED, text="⏸️ Pause", command=self.pause_generation)
        self.stop_btn.config(state=tk.DISABLED)
        self.progress_var.set(0)
        self.status_var.set("Ready to generate video")

    def open_recent_video(self):
        """Open the most recent video"""
        if self.most_recent_video and os.path.exists(self.most_recent_video):
            try:
                if platform.system() == "Windows":
                    os.startfile(self.most_recent_video)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", self.most_recent_video])
                else:  # Linux
                    subprocess.run(["xdg-open", self.most_recent_video])
                self.log_message(f"Opened video: {self.most_recent_video}")
            except Exception as e:
                self.log_message(f"Failed to open video: {str(e)}")
                messagebox.showerror("Error", f"Failed to open video:\n{str(e)}")

    def open_video_folder(self):
        """Open the folder containing the most recent video"""
        if self.most_recent_video and os.path.exists(self.most_recent_video):
            folder_path = os.path.dirname(self.most_recent_video)
            try:
                if platform.system() == "Windows":
                    subprocess.run(["explorer", folder_path])
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(["open", folder_path])
                else:  # Linux
                    subprocess.run(["xdg-open", folder_path])
                self.log_message(f"Opened folder: {folder_path}")
            except Exception as e:
                self.log_message(f"Failed to open folder: {str(e)}")
                messagebox.showerror("Error", f"Failed to open folder:\n{str(e)}")

    def refresh_resources(self):
        """Refresh available resources"""
        self.log_message("Refreshing resources...")

        # Refresh voices
        self.load_voices_for_tts_model()
        if hasattr(self, 'voice_widget'):
            self.voice_widget.configure(values=self.current_voices)

        # Refresh music files
        music_options = self.get_music_files()
        self.bg_music_combo.configure(values=music_options)

        # Refresh fonts
        available_fonts = get_available_fonts()
        if available_fonts:
            self.font_combo.configure(values=available_fonts)

        self.log_message("Resources refreshed")

    def show_settings(self):
        """Show comprehensive settings dialog"""
        self.settings_dialog = ModernSettingsDialog(self.root, self)
        self.settings_dialog.show()


# Main application entry point
def main():
    """Main entry point for the modern UI"""
    # Create root window first
    root = ThemedTk(theme="arc")

    # Set initial size and position for login
    screen_width = root.winfo_screenwidth()
    screen_height = root.winfo_screenheight()
    root.geometry(f"1400x800+{(screen_width - 1400) // 2}+{(screen_height - 800) // 2}")

    # Check if user is authenticated
    if not show_login_dialog(root):
        print("Authentication failed. Exiting.")
        root.destroy()
        sys.exit(1)

    # Create and run the modern application
    app = ModernApp(root)
    root.mainloop()


if __name__ == "__main__":
    main()
