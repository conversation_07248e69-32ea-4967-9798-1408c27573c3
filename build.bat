@echo off
setlocal EnableDelayedExpansion
title Building Faceless Video Generator v6.5

echo =====================================================
echo   Building Secure Distribution for Faceless Video Generator v6.5
echo =====================================================
echo.
echo This window will stay open after completion. Please do not close it.
echo.

:: Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat || (
    echo ERROR: Failed to activate virtual environment.
    echo Make sure venv exists and is set up correctly.
    echo.
    pause
    exit /b 1
)
echo Virtual environment activated successfully.
echo.

:: Install simple obfuscator tool
echo Installing simple code obfuscator...
pip install pyminifier 2>nul || (
    echo Failed to install pyminifier. Using alternative method...
)

:: Create secure distribution directory
if exist "Secure_Distribution" rmdir /s /q "Secure_Distribution"
mkdir "Secure_Distribution"
mkdir "Secure_Distribution\src"

:: Create all subdirectories in src
mkdir "Secure_Distribution\src\services"
mkdir "Secure_Distribution\src\assets"
mkdir "Secure_Distribution\src\data"
mkdir "Secure_Distribution\src\test_audio"
mkdir "Secure_Distribution\src\test_output"

:: Create external resource directories
mkdir "Secure_Distribution\resources"
mkdir "Secure_Distribution\font"
mkdir "Secure_Distribution\data"
mkdir "Secure_Distribution\assets"

:: Copy resources and config files
echo Copying resource files and directories...
if exist "resources" xcopy "resources" "Secure_Distribution\resources\" /E /H /C /I /y
if exist "font" xcopy "font" "Secure_Distribution\font\" /E /H /C /I /y
if exist "assets" xcopy "assets" "Secure_Distribution\assets\" /E /H /C /I /y
if exist "data" xcopy "data" "Secure_Distribution\data\" /E /H /C /I /y

:: Copy src subdirectory assets
if exist "src\assets" xcopy "src\assets" "Secure_Distribution\src\assets\" /E /H /C /I /y
if exist "src\test_audio" xcopy "src\test_audio" "Secure_Distribution\src\test_audio\" /E /H /C /I /y
if exist "src\test_output" xcopy "src\test_output" "Secure_Distribution\src\test_output\" /E /H /C /I /y

:: Copy JSON and other non-Python files from src
for %%f in (src\*.json) do (
    echo Copying %%f...
    copy "%%f" "Secure_Distribution\%%f"
)

:: Copy other important files
if exist ".env" copy ".env" "Secure_Distribution\"
if exist ".env.example" copy ".env.example" "Secure_Distribution\"
if exist "README.md" copy "README.md" "Secure_Distribution\"
if exist "requirements.txt" copy "requirements.txt" "Secure_Distribution\"
if exist "config.json" copy "config.json" "Secure_Distribution\"

:: Create __init__.py files to make proper packages
echo # Simple Obfuscator Runtime > "Secure_Distribution\src\__init__.py"
echo # Make src a proper package that can be imported > "Secure_Distribution\src\__init__.py"

:: Process Python files
echo.
echo Processing Python files...

:: Create a better obfuscator script that handles Windows paths correctly
echo import base64, zlib, random, string, os > obfuscator.py
echo. >> obfuscator.py
echo def obfuscate(input_path, output_path): >> obfuscator.py
echo     try: >> obfuscator.py
echo         print(f"Obfuscating {input_path}...") >> obfuscator.py
echo         # Handle Windows paths correctly by normalizing them >> obfuscator.py
echo         input_path = os.path.normpath(input_path) >> obfuscator.py
echo         output_path = os.path.normpath(output_path) >> obfuscator.py
echo         # Ensure output directory exists >> obfuscator.py
echo         os.makedirs(os.path.dirname(output_path), exist_ok=True) >> obfuscator.py
echo         with open(input_path, 'rb') as f: >> obfuscator.py
echo             content = f.read() >> obfuscator.py
echo         # Compress and encode the content >> obfuscator.py
echo         compressed = zlib.compress(content, 9) >> obfuscator.py
echo         encoded = base64.b85encode(compressed) >> obfuscator.py
echo         # Generate random variable names >> obfuscator.py
echo         var1 = ''.join(random.choices(string.ascii_lowercase, k=10)) >> obfuscator.py
echo         var2 = ''.join(random.choices(string.ascii_lowercase, k=10)) >> obfuscator.py
echo         var3 = ''.join(random.choices(string.ascii_lowercase, k=10)) >> obfuscator.py
echo         # Create obfuscated code >> obfuscator.py
echo         obfuscated = f"""import zlib, base64\n{var1} = {repr(encoded)}\n{var2} = base64.b85decode({var1})\n{var3} = zlib.decompress({var2})\nexec({var3})""" >> obfuscator.py
echo         # Write to output file >> obfuscator.py
echo         with open(output_path, 'w') as f: >> obfuscator.py
echo             f.write(obfuscated) >> obfuscator.py
echo         return True >> obfuscator.py
echo     except Exception as e: >> obfuscator.py
echo         print(f"Error in obfuscation: {e}") >> obfuscator.py
echo         return False >> obfuscator.py

:: Process all Python files recursively with proper path handling
echo.
echo Processing all Python files in src directory...

:: Create __init__.py files for all subdirectories to make proper packages
echo # Services module > "Secure_Distribution\src\services\__init__.py"
echo # Assets module > "Secure_Distribution\src\assets\__init__.py"
echo # Data module > "Secure_Distribution\src\data\__init__.py"
echo # Test audio module > "Secure_Distribution\src\test_audio\__init__.py"
echo # Test output module > "Secure_Distribution\src\test_output\__init__.py"

:: Process root Python files
for %%f in (src\*.py) do (
    echo Processing %%f...
    if "%%~nxf" NEQ "__init__.py" (
        python -c "import os; from obfuscator import obfuscate; obfuscate(r'%%f', r'Secure_Distribution\%%f')" || (
            echo Simple obfuscation failed for %%f, copying original...
            copy "%%f" "Secure_Distribution\%%f"
        )
    )
)

:: Process services directory Python files
echo.
echo Processing services directory Python files...
if exist "src\services" (
    for %%f in (src\services\*.py) do (
        echo Processing %%f...
        if "%%~nxf" NEQ "__init__.py" (
            python -c "import os; from obfuscator import obfuscate; obfuscate(r'%%f', r'Secure_Distribution\%%f')" || (
                echo Simple obfuscation failed for %%f, copying original...
                copy "%%f" "Secure_Distribution\%%f"
            )
        )
    )
) else (
    echo WARNING: services directory not found in src. This may cause import errors.
)

:: Process any other Python files in subdirectories
echo.
echo Processing other subdirectory Python files...
for /r "src" %%f in (*.py) do (
    :: Skip files in root src and services directories (already processed)
    set "filepath=%%f"
    set "dirpath=%%~dpf"
    set "dirpath=!dirpath:~0,-1!"

    if /i "!dirpath!" NEQ "%CD%\src" if /i "!dirpath!" NEQ "%CD%\src\services" (
        if "%%~nxf" NEQ "__init__.py" (
            echo Processing %%f...
            set "relpath=%%f"
            set "relpath=!relpath:%CD%\=!"
            python -c "import os; from obfuscator import obfuscate; obfuscate(r'%%f', r'Secure_Distribution\!relpath!')" || (
                echo Simple obfuscation failed for %%f, copying original...

                :: Create directory if it doesn't exist
                set "targetdir=Secure_Distribution\!relpath:%%~nxf=!"
                if not exist "!targetdir!" mkdir "!targetdir!"

                copy "%%f" "Secure_Distribution\!relpath!"
            )
        )
    )
)

:: Create main runner script with venv check
echo Creating run.bat...
echo @echo off > "Secure_Distribution\run.bat"
echo setlocal EnableDelayedExpansion >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo echo ======================================================= >> "Secure_Distribution\run.bat"
echo echo   Running Faceless Video Generator v6.5 >> "Secure_Distribution\run.bat"
echo echo ======================================================= >> "Secure_Distribution\run.bat"
echo echo. >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo :: Check if venv exists and activate it >> "Secure_Distribution\run.bat"
echo if exist "venv\Scripts\activate.bat" ( >> "Secure_Distribution\run.bat"
echo     echo Activating virtual environment... >> "Secure_Distribution\run.bat"
echo     call venv\Scripts\activate.bat >> "Secure_Distribution\run.bat"
echo     echo Virtual environment activated successfully. >> "Secure_Distribution\run.bat"
echo ) else ( >> "Secure_Distribution\run.bat"
echo     echo No virtual environment found. Running with system Python... >> "Secure_Distribution\run.bat"
echo ) >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo :: Create default config file if it doesn't exist >> "Secure_Distribution\run.bat"
echo if not exist "config.json" ( >> "Secure_Distribution\run.bat"
echo     echo Creating default config file... >> "Secure_Distribution\run.bat"
echo     echo { > "config.json" >> "Secure_Distribution\run.bat"
echo     echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo   "whatsapp_number": "+923107520004", >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo   "tts": { >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo     "speech_rate": 1.0 >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo   } >> "config.json" >> "Secure_Distribution\run.bat"
echo     echo } >> "config.json" >> "Secure_Distribution\run.bat"
echo ) >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo :: Check for MoviePy installation >> "Secure_Distribution\run.bat"
echo python -c "import moviepy" 2>nul >> "Secure_Distribution\run.bat"
echo if errorlevel 1 ( >> "Secure_Distribution\run.bat"
echo     echo MoviePy package not found. Installing it now... >> "Secure_Distribution\run.bat"
echo     pip install moviepy >> "Secure_Distribution\run.bat"
echo     if errorlevel 1 ( >> "Secure_Distribution\run.bat"
echo         echo Failed to install MoviePy. Trying alternate method... >> "Secure_Distribution\run.bat"
echo         pip install --upgrade pip >> "Secure_Distribution\run.bat"
echo         pip install moviepy==1.0.3 decorator==4.4.2 imageio==2.35.1 >> "Secure_Distribution\run.bat"
echo         if errorlevel 1 ( >> "Secure_Distribution\run.bat"
echo             echo ERROR: Could not install MoviePy. Please install it manually by running: >> "Secure_Distribution\run.bat"
echo             echo pip install moviepy >> "Secure_Distribution\run.bat"
echo             pause >> "Secure_Distribution\run.bat"
echo         ) else ( >> "Secure_Distribution\run.bat"
echo             echo MoviePy installed successfully! >> "Secure_Distribution\run.bat"
echo         ) >> "Secure_Distribution\run.bat"
echo     ) else ( >> "Secure_Distribution\run.bat"
echo         echo MoviePy installed successfully! >> "Secure_Distribution\run.bat"
echo     ) >> "Secure_Distribution\run.bat"
echo ) >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo echo Setting up Python path... >> "Secure_Distribution\run.bat"
echo set PYTHONPATH=%CD% >> "Secure_Distribution\run.bat"
echo echo Starting application... >> "Secure_Distribution\run.bat"
echo python src\main.py >> "Secure_Distribution\run.bat"
echo. >> "Secure_Distribution\run.bat"
echo echo Application closed. Press any key to exit... >> "Secure_Distribution\run.bat"
echo pause >> "Secure_Distribution\run.bat"

:: Create a proper config.json file with all required sections
echo Creating config.json...
echo { > "Secure_Distribution\config.json"
echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", >> "Secure_Distribution\config.json"
echo   "whatsapp_number": "+923107520004", >> "Secure_Distribution\config.json"
echo   "tts": { >> "Secure_Distribution\config.json"
echo     "speech_rate": 1.0 >> "Secure_Distribution\config.json"
echo   } >> "Secure_Distribution\config.json"
echo } >> "Secure_Distribution\config.json"

:: Create advanced installation script that checks Python version and sets up venv
echo Creating install.bat...
echo @echo off > "Secure_Distribution\install.bat"
echo setlocal EnableDelayedExpansion >> "Secure_Distribution\install.bat"
echo title Installing Faceless Video Generator v6.5 >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo   Installing Faceless Video Generator v6.5 >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo :: Check if Python 3.12.8 is installed >> "Secure_Distribution\install.bat"
echo echo Checking Python installation... >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo python --version 2>nul | findstr "3.12.8" > nul >> "Secure_Distribution\install.bat"
echo if errorlevel 1 ( >> "Secure_Distribution\install.bat"
echo     echo Python 3.12.8 is not installed or not in PATH. >> "Secure_Distribution\install.bat"
echo     echo Downloading Python 3.12.8 installer... >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo     :: Download Python 3.12.8 installer using PowerShell >> "Secure_Distribution\install.bat"
echo     powershell -Command "& {Invoke-WebRequest -Uri 'https://www.python.org/ftp/python/3.12.8/python-3.12.8-amd64.exe' -OutFile 'python-3.12.8-amd64.exe'}" >> "Secure_Distribution\install.bat"
echo     if not exist "python-3.12.8-amd64.exe" ( >> "Secure_Distribution\install.bat"
echo         echo Failed to download Python installer. >> "Secure_Distribution\install.bat"
echo         echo Please manually install Python 3.12.8 from https://www.python.org/downloads/ >> "Secure_Distribution\install.bat"
echo         echo Then run this install script again. >> "Secure_Distribution\install.bat"
echo         pause >> "Secure_Distribution\install.bat"
echo         exit /b 1 >> "Secure_Distribution\install.bat"
echo     ) >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo     echo Installing Python 3.12.8... >> "Secure_Distribution\install.bat"
echo     echo Please complete the installation when the installer opens. >> "Secure_Distribution\install.bat"
echo     echo IMPORTANT: Check "Add Python to PATH" during installation. >> "Secure_Distribution\install.bat"
echo     python-3.12.8-amd64.exe /passive InstallAllUsers=1 PrependPath=1 >> "Secure_Distribution\install.bat"
echo     echo. >> "Secure_Distribution\install.bat"
echo     echo Waiting for Python installation to complete... >> "Secure_Distribution\install.bat"
echo     timeout /t 30 >> "Secure_Distribution\install.bat"
echo     del python-3.12.8-amd64.exe >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo     :: Verify Python was installed correctly >> "Secure_Distribution\install.bat"
echo     python --version 2>nul | findstr "3.12" > nul >> "Secure_Distribution\install.bat"
echo     if errorlevel 1 ( >> "Secure_Distribution\install.bat"
echo         echo. >> "Secure_Distribution\install.bat"
echo         echo ERROR: Python 3.12.8 installation failed or PATH was not updated. >> "Secure_Distribution\install.bat"
echo         echo Please restart your computer and run this script again. >> "Secure_Distribution\install.bat"
echo         echo If the problem persists, install Python 3.12.8 manually. >> "Secure_Distribution\install.bat"
echo         pause >> "Secure_Distribution\install.bat"
echo         exit /b 1 >> "Secure_Distribution\install.bat"
echo     ) >> "Secure_Distribution\install.bat"
echo ) >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Python 3.12 is installed and ready to use. >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo :: Create and activate virtual environment >> "Secure_Distribution\install.bat"
echo echo Creating virtual environment... >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo if exist "venv" rmdir /s /q "venv" >> "Secure_Distribution\install.bat"
echo python -m venv venv >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Activating virtual environment... >> "Secure_Distribution\install.bat"
echo call venv\Scripts\activate.bat >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Upgrading pip... >> "Secure_Distribution\install.bat"
echo python -m pip install --upgrade pip >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Installing required dependencies... >> "Secure_Distribution\install.bat"
echo pip install -r requirements.txt >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Installing additional dependencies... >> "Secure_Distribution\install.bat"
echo pip install together >> "Secure_Distribution\install.bat"
echo pip install moviepy==1.0.3 decorator==4.4.2 imageio==2.35.1 imageio-ffmpeg==0.5.1 proglog==0.1.10 >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo Creating default configuration... >> "Secure_Distribution\install.bat"
echo echo { > "config.json" >> "Secure_Distribution\install.bat"
echo echo   "spreadsheet_id": "1kOOVtONxmaVQa2qnuSWcLP-82kmbZDVKGc2ljOfiZ10", >> "config.json" >> "Secure_Distribution\install.bat"
echo echo   "whatsapp_number": "+923107520004", >> "config.json" >> "Secure_Distribution\install.bat"
echo echo   "tts": { >> "config.json" >> "Secure_Distribution\install.bat"
echo echo     "speech_rate": 1.0 >> "config.json" >> "Secure_Distribution\install.bat"
echo echo   } >> "config.json" >> "Secure_Distribution\install.bat"
echo echo } >> "config.json" >> "Secure_Distribution\install.bat"
echo. >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo Installation complete! >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo echo Setting up Python path for modules... >> "Secure_Distribution\install.bat"
echo echo # Make sure src directory is in Python path > "Secure_Distribution\src\__init__.py" >> "Secure_Distribution\install.bat"
echo echo # Make services a proper package > "Secure_Distribution\src\services\__init__.py" >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo echo You can now run the application using run.bat >> "Secure_Distribution\install.bat"
echo echo ======================================================= >> "Secure_Distribution\install.bat"
echo echo. >> "Secure_Distribution\install.bat"
echo pause >> "Secure_Distribution\install.bat"

:: Create README file
echo Creating README.txt...
echo # Faceless Video Generator v6.5 > "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo ## Installation >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo 1. Double-click install.bat to set up the application >> "Secure_Distribution\README.txt"
echo    - This will check if Python 3.12.8 is installed >> "Secure_Distribution\README.txt"
echo    - If Python is not installed, it will download and install it >> "Secure_Distribution\README.txt"
echo    - It will create a virtual environment and install all required dependencies >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo 2. After installation, run the application by double-clicking run.bat >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo ## Login >> "Secure_Distribution\README.txt"
echo. >> "Secure_Distribution\README.txt"
echo You will need a valid login to use the application. If you don't have access yet, >> "Secure_Distribution\README.txt"
echo you can purchase a license by clicking the "Purchase License" button. >> "Secure_Distribution\README.txt"

:: Clean up
echo Cleaning up temporary files...
if exist "obfuscator.py" del "obfuscator.py"

echo.
echo Build completed successfully for Faceless Video Generator v6.5!
echo Secure files are in the Secure_Distribution folder.
echo Files have been obfuscated to protect your source code.
echo.
echo Press any key to exit...
pause > nul